{"name": "yu-reader", "version": "1.0.0", "description": "现代化的跨平台桌面电子书阅读器 - 专注于学习场景的智能化阅读体验", "main": "dist/main/index.js", "author": "Yu Reader Team", "license": "MIT", "private": true, "keywords": ["electron", "vue3", "ebook", "reader", "learning", "ai", "desktop"], "homepage": "https://github.com/your-org/yu-reader", "repository": {"type": "git", "url": "https://github.com/your-org/yu-reader.git"}, "bugs": {"url": "https://github.com/your-org/yu-reader/issues"}, "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "package": "electron-builder", "package:win": "electron-builder --win", "package:mac": "electron-builder --mac", "package:linux": "electron-builder --linux", "package:all": "electron-builder --win --mac --linux", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts,.vue --fix", "lint:check": "eslint . --ext .ts,.vue", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "clean": "rimraf dist node_modules/.vite", "rebuild": "npm run clean && npm install", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "better-sqlite3": "^9.2.2", "element-plus": "^2.4.4", "epub": "^1.2.0", "fuse.js": "^7.0.0", "jszip": "^3.10.1", "mammoth": "^1.6.0", "pdfjs-dist": "^3.11.174", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.3", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.40.1", "@types/better-sqlite3": "^7.6.8", "@types/node": "^20.10.6", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.3", "electron": "^28.1.4", "electron-builder": "^24.9.1", "electron-vite": "^2.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.1.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11", "vitest": "^1.1.1", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "build": {"appId": "com.yureader.app", "productName": "<PERSON>", "copyright": "Copyright © 2025 Yu Reader Team", "directories": {"output": "release"}, "files": ["dist/**/*", "resources/**/*", "!**/node_modules/**/*", "!src/**/*", "!tests/**/*", "!docs/**/*"], "extraResources": [{"from": "resources/", "to": "resources/"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icons/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icons/icon.icns", "category": "public.app-category.productivity", "hardenedRuntime": true, "gatekeeperAssess": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/icons/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON>"}, "dmg": {"title": "Yu Reader ${version}", "backgroundColor": "#ffffff", "window": {"width": 540, "height": 380}, "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "publish": {"provider": "github", "owner": "your-org", "repo": "yu-reader"}}}