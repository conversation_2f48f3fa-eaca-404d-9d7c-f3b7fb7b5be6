import { ipcMain } from 'electron'
import { getDatabase } from '../database'
import { wrapIPCHandler } from './index'
import type { Book, CreateBookDto, UpdateBookDto } from '@shared/types/book'

/**
 * 图书相关IPC处理器
 */

export function setupBookIPC(): void {
  // 获取所有图书
  ipcMain.handle('books:findAll', wrapIPCHandler(async (): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare(`
      SELECT * FROM books 
      ORDER BY updated_at DESC
    `)
    return stmt.all() as Book[]
  }))

  // 根据ID获取图书
  ipcMain.handle('books:findById', wrapIPCHandler(async (_, id: number): Promise<Book | null> => {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM books WHERE id = ?')
    const result = stmt.get(id) as Book | undefined
    return result || null
  }))

  // 搜索图书
  ipcMain.handle('books:search', wrapIPCHandler(async (_, query: string): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare(`
      SELECT * FROM books 
      WHERE title LIKE ? OR author LIKE ? OR description LIKE ?
      ORDER BY updated_at DESC
    `)
    const searchTerm = `%${query}%`
    return stmt.all(searchTerm, searchTerm, searchTerm) as Book[]
  }))

  // 根据格式筛选图书
  ipcMain.handle('books:findByFormat', wrapIPCHandler(async (_, format: string): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM books WHERE format = ? ORDER BY updated_at DESC')
    return stmt.all(format) as Book[]
  }))

  // 根据作者筛选图书
  ipcMain.handle('books:findByAuthor', wrapIPCHandler(async (_, author: string): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM books WHERE author = ? ORDER BY updated_at DESC')
    return stmt.all(author) as Book[]
  }))

  // 创建图书记录
  ipcMain.handle('books:create', wrapIPCHandler(async (_, bookData: CreateBookDto): Promise<Book> => {
    const db = getDatabase()
    const stmt = db.prepare(`
      INSERT INTO books (
        title, author, isbn, format, file_path, file_size, 
        cover_path, language, publisher, publish_date, 
        description, tags, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)
    
    const result = stmt.run(
      bookData.title,
      bookData.author || null,
      bookData.isbn || null,
      bookData.format,
      bookData.filePath,
      bookData.fileSize || null,
      bookData.coverPath || null,
      bookData.language || null,
      bookData.publisher || null,
      bookData.publishDate || null,
      bookData.description || null,
      bookData.tags ? JSON.stringify(bookData.tags) : null,
      bookData.metadata ? JSON.stringify(bookData.metadata) : null
    )

    // 获取创建的图书
    const getStmt = db.prepare('SELECT * FROM books WHERE id = ?')
    return getStmt.get(result.lastInsertRowid) as Book
  }))

  // 更新图书信息
  ipcMain.handle('books:update', wrapIPCHandler(async (_, id: number, bookData: UpdateBookDto): Promise<Book> => {
    const db = getDatabase()
    
    // 构建动态更新语句
    const updateFields: string[] = []
    const values: any[] = []
    
    if (bookData.title !== undefined) {
      updateFields.push('title = ?')
      values.push(bookData.title)
    }
    if (bookData.author !== undefined) {
      updateFields.push('author = ?')
      values.push(bookData.author)
    }
    if (bookData.isbn !== undefined) {
      updateFields.push('isbn = ?')
      values.push(bookData.isbn)
    }
    if (bookData.coverPath !== undefined) {
      updateFields.push('cover_path = ?')
      values.push(bookData.coverPath)
    }
    if (bookData.language !== undefined) {
      updateFields.push('language = ?')
      values.push(bookData.language)
    }
    if (bookData.publisher !== undefined) {
      updateFields.push('publisher = ?')
      values.push(bookData.publisher)
    }
    if (bookData.publishDate !== undefined) {
      updateFields.push('publish_date = ?')
      values.push(bookData.publishDate)
    }
    if (bookData.description !== undefined) {
      updateFields.push('description = ?')
      values.push(bookData.description)
    }
    if (bookData.tags !== undefined) {
      updateFields.push('tags = ?')
      values.push(JSON.stringify(bookData.tags))
    }
    if (bookData.metadata !== undefined) {
      updateFields.push('metadata = ?')
      values.push(JSON.stringify(bookData.metadata))
    }
    
    // 添加更新时间
    updateFields.push('updated_at = CURRENT_TIMESTAMP')
    values.push(id)

    const stmt = db.prepare(`
      UPDATE books 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `)
    
    stmt.run(...values)

    // 获取更新后的图书
    const getStmt = db.prepare('SELECT * FROM books WHERE id = ?')
    return getStmt.get(id) as Book
  }))

  // 删除图书
  ipcMain.handle('books:delete', wrapIPCHandler(async (_, id: number): Promise<void> => {
    const db = getDatabase()
    const stmt = db.prepare('DELETE FROM books WHERE id = ?')
    stmt.run(id)
  }))

  // 批量删除图书
  ipcMain.handle('books:batchDelete', wrapIPCHandler(async (_, ids: number[]): Promise<void> => {
    const db = getDatabase()
    const placeholders = ids.map(() => '?').join(',')
    const stmt = db.prepare(`DELETE FROM books WHERE id IN (${placeholders})`)
    stmt.run(...ids)
  }))

  // 获取图书统计信息
  ipcMain.handle('books:getStats', wrapIPCHandler(async (): Promise<{
    total: number
    byFormat: Record<string, number>
    byLanguage: Record<string, number>
    recentlyAdded: number
  }> => {
    const db = getDatabase()
    
    // 总数
    const totalStmt = db.prepare('SELECT COUNT(*) as count FROM books')
    const total = (totalStmt.get() as { count: number }).count
    
    // 按格式统计
    const formatStmt = db.prepare('SELECT format, COUNT(*) as count FROM books GROUP BY format')
    const formatResults = formatStmt.all() as { format: string; count: number }[]
    const byFormat = formatResults.reduce((acc, item) => {
      acc[item.format] = item.count
      return acc
    }, {} as Record<string, number>)
    
    // 按语言统计
    const languageStmt = db.prepare('SELECT language, COUNT(*) as count FROM books WHERE language IS NOT NULL GROUP BY language')
    const languageResults = languageStmt.all() as { language: string; count: number }[]
    const byLanguage = languageResults.reduce((acc, item) => {
      acc[item.language] = item.count
      return acc
    }, {} as Record<string, number>)
    
    // 最近添加的图书数量（7天内）
    const recentStmt = db.prepare('SELECT COUNT(*) as count FROM books WHERE created_at >= datetime("now", "-7 days")')
    const recentlyAdded = (recentStmt.get() as { count: number }).count
    
    return {
      total,
      byFormat,
      byLanguage,
      recentlyAdded
    }
  }))

  // 获取最近阅读的图书
  ipcMain.handle('books:getRecentlyRead', wrapIPCHandler(async (limit: number = 10): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare(`
      SELECT b.* FROM books b
      INNER JOIN reading_progress rp ON b.id = rp.book_id
      ORDER BY rp.last_read_at DESC
      LIMIT ?
    `)
    return stmt.all(limit) as Book[]
  }))

  // 获取收藏的图书
  ipcMain.handle('books:getFavorites', wrapIPCHandler(async (): Promise<Book[]> => {
    const db = getDatabase()
    const stmt = db.prepare(`
      SELECT * FROM books 
      WHERE JSON_EXTRACT(metadata, '$.favorite') = true
      ORDER BY updated_at DESC
    `)
    return stmt.all() as Book[]
  }))

  // 设置图书收藏状态
  ipcMain.handle('books:setFavorite', wrapIPCHandler(async (_, id: number, favorite: boolean): Promise<void> => {
    const db = getDatabase()
    
    // 获取当前metadata
    const getStmt = db.prepare('SELECT metadata FROM books WHERE id = ?')
    const result = getStmt.get(id) as { metadata: string | null }
    
    let metadata = {}
    if (result?.metadata) {
      try {
        metadata = JSON.parse(result.metadata)
      } catch (error) {
        console.error('解析metadata失败:', error)
      }
    }
    
    // 更新收藏状态
    metadata = { ...metadata, favorite }
    
    const updateStmt = db.prepare('UPDATE books SET metadata = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?')
    updateStmt.run(JSON.stringify(metadata), id)
  }))

  // 检查文件路径是否已存在
  ipcMain.handle('books:checkFileExists', wrapIPCHandler(async (_, filePath: string): Promise<boolean> => {
    const db = getDatabase()
    const stmt = db.prepare('SELECT COUNT(*) as count FROM books WHERE file_path = ?')
    const result = stmt.get(filePath) as { count: number }
    return result.count > 0
  }))
}
