/**
 * 图书相关类型定义
 */

/**
 * 图书格式枚举
 */
export enum BookFormat {
  EPUB = 'epub',
  PDF = 'pdf',
  TXT = 'txt',
  MOBI = 'mobi',
  AZW3 = 'azw3',
  DOCX = 'docx',
  RTF = 'rtf',
  HTML = 'html'
}

/**
 * 图书语言枚举
 */
export enum BookLanguage {
  ZH_CN = 'zh-CN',
  ZH_TW = 'zh-TW',
  EN = 'en',
  JA = 'ja',
  KO = 'ko',
  FR = 'fr',
  DE = 'de',
  ES = 'es',
  RU = 'ru',
  IT = 'it',
  PT = 'pt',
  AR = 'ar'
}

/**
 * 图书元数据接口
 */
export interface BookMetadata {
  /** 是否收藏 */
  favorite?: boolean
  /** 阅读状态 */
  readingStatus?: 'unread' | 'reading' | 'finished'
  /** 评分 (1-5) */
  rating?: number
  /** 用户评论 */
  review?: string
  /** 自定义字段 */
  [key: string]: any
}

/**
 * 图书位置信息接口
 */
export interface BookPosition {
  /** 章节ID或页码 */
  chapter?: string | number
  /** 段落或行号 */
  paragraph?: number
  /** 字符偏移量 */
  offset?: number
  /** 百分比位置 */
  percentage?: number
  /** 格式特定的位置信息 */
  formatSpecific?: Record<string, any>
}

/**
 * 图书实体接口
 */
export interface Book {
  /** 图书ID */
  id: number
  /** 图书标题 */
  title: string
  /** 作者 */
  author?: string
  /** ISBN */
  isbn?: string
  /** 文件格式 */
  format: BookFormat
  /** 文件路径 */
  file_path: string
  /** 文件大小（字节） */
  file_size?: number
  /** 封面图片路径 */
  cover_path?: string
  /** 语言 */
  language?: BookLanguage
  /** 出版社 */
  publisher?: string
  /** 出版日期 */
  publish_date?: string
  /** 描述/简介 */
  description?: string
  /** 标签列表 */
  tags?: string[]
  /** 元数据 */
  metadata?: BookMetadata
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 创建图书DTO
 */
export interface CreateBookDto {
  /** 图书标题 */
  title: string
  /** 作者 */
  author?: string
  /** ISBN */
  isbn?: string
  /** 文件格式 */
  format: BookFormat
  /** 文件路径 */
  filePath: string
  /** 文件大小（字节） */
  fileSize?: number
  /** 封面图片路径 */
  coverPath?: string
  /** 语言 */
  language?: BookLanguage
  /** 出版社 */
  publisher?: string
  /** 出版日期 */
  publishDate?: string
  /** 描述/简介 */
  description?: string
  /** 标签列表 */
  tags?: string[]
  /** 元数据 */
  metadata?: BookMetadata
}

/**
 * 更新图书DTO
 */
export interface UpdateBookDto {
  /** 图书标题 */
  title?: string
  /** 作者 */
  author?: string
  /** ISBN */
  isbn?: string
  /** 封面图片路径 */
  coverPath?: string
  /** 语言 */
  language?: BookLanguage
  /** 出版社 */
  publisher?: string
  /** 出版日期 */
  publishDate?: string
  /** 描述/简介 */
  description?: string
  /** 标签列表 */
  tags?: string[]
  /** 元数据 */
  metadata?: BookMetadata
}

/**
 * 图书搜索选项
 */
export interface BookSearchOptions {
  /** 搜索关键词 */
  query?: string
  /** 作者筛选 */
  author?: string
  /** 格式筛选 */
  format?: BookFormat
  /** 语言筛选 */
  language?: BookLanguage
  /** 标签筛选 */
  tags?: string[]
  /** 阅读状态筛选 */
  readingStatus?: 'unread' | 'reading' | 'finished'
  /** 是否只显示收藏 */
  favoritesOnly?: boolean
  /** 排序字段 */
  sortBy?: 'title' | 'author' | 'created_at' | 'updated_at' | 'last_read_at'
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 分页：页码 */
  page?: number
  /** 分页：每页数量 */
  limit?: number
}

/**
 * 图书搜索结果
 */
export interface BookSearchResult {
  /** 图书列表 */
  books: Book[]
  /** 总数量 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页数量 */
  limit: number
  /** 总页数 */
  totalPages: number
}

/**
 * 图书统计信息
 */
export interface BookStats {
  /** 总图书数 */
  total: number
  /** 按格式统计 */
  byFormat: Record<string, number>
  /** 按语言统计 */
  byLanguage: Record<string, number>
  /** 按阅读状态统计 */
  byReadingStatus: Record<string, number>
  /** 最近添加的图书数量 */
  recentlyAdded: number
  /** 收藏图书数量 */
  favorites: number
}

/**
 * 图书导入结果
 */
export interface BookImportResult {
  /** 成功导入的图书 */
  success: Book[]
  /** 导入失败的文件 */
  failed: Array<{
    filePath: string
    error: string
  }>
  /** 跳过的文件（已存在） */
  skipped: string[]
}

/**
 * 图书导入选项
 */
export interface BookImportOptions {
  /** 是否覆盖已存在的图书 */
  overwrite?: boolean
  /** 是否递归扫描子目录 */
  recursive?: boolean
  /** 是否自动提取元数据 */
  extractMetadata?: boolean
  /** 是否生成缩略图 */
  generateThumbnail?: boolean
  /** 支持的文件格式 */
  supportedFormats?: BookFormat[]
}
