import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { createMainWindow } from './window'
import { setupMenu } from './menu'
import { initDatabase } from './database'
import { setupIPC } from './ipc'
import icon from '../../resources/icon.png?asset'

/**
 * Yu Reader 主进程入口
 * 负责应用生命周期管理、窗口创建、IPC通信设置等
 */

let mainWindow: BrowserWindow | null = null

/**
 * 应用准备就绪时的处理
 */
async function handleAppReady(): Promise<void> {
  // 设置应用用户模型ID (Windows)
  electronApp.setAppUserModelId('com.yureader.app')

  // 开发环境下的默认打开或关闭DevTools快捷键
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // 初始化数据库
  try {
    await initDatabase()
    console.log('数据库初始化成功')
  } catch (error) {
    console.error('数据库初始化失败:', error)
  }

  // 设置IPC通信
  setupIPC()

  // 创建主窗口
  mainWindow = createMainWindow(icon)

  // 设置应用菜单
  setupMenu()

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 开发环境下打开DevTools
  if (is.dev) {
    mainWindow.webContents.openDevTools()
  }
}

/**
 * 应用激活时的处理 (macOS)
 */
function handleAppActivate(): void {
  // 在macOS上，当点击dock图标且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    mainWindow = createMainWindow(icon)
  }
}

/**
 * 所有窗口关闭时的处理
 */
function handleWindowAllClosed(): void {
  // 在macOS上，应用通常会保持活跃状态，即使所有窗口都关闭了
  // 直到用户明确地使用Cmd + Q退出
  if (process.platform !== 'darwin') {
    app.quit()
  }
}

/**
 * 应用即将退出时的处理
 */
function handleBeforeQuit(): void {
  console.log('应用即将退出，执行清理操作...')
  // 这里可以添加清理逻辑，如保存用户数据、关闭数据库连接等
}

/**
 * 处理未捕获的异常
 */
function handleUncaughtException(error: Error): void {
  console.error('未捕获的异常:', error)
  // 在生产环境中，可以将错误发送到错误报告服务
}

/**
 * 处理未处理的Promise拒绝
 */
function handleUnhandledRejection(reason: any, promise: Promise<any>): void {
  console.error('未处理的Promise拒绝:', reason, promise)
  // 在生产环境中，可以将错误发送到错误报告服务
}

// 注册事件监听器
app.whenReady().then(handleAppReady)
app.on('activate', handleAppActivate)
app.on('window-all-closed', handleWindowAllClosed)
app.on('before-quit', handleBeforeQuit)

// 错误处理
process.on('uncaughtException', handleUncaughtException)
process.on('unhandledRejection', handleUnhandledRejection)

// 安全设置：阻止新窗口创建
app.on('web-contents-created', (_, contents) => {
  contents.on('new-window', (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    // 只允许打开同源链接
    if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.origin !== 'file://') {
      navigationEvent.preventDefault()
      shell.openExternal(navigationUrl)
    }
  })
})

// 导出主窗口引用供其他模块使用
export { mainWindow }
