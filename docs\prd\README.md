# 📋 产品需求文档 (PRD) 目录

## 目录用途

本目录存储所有产品需求相关的文档，支持BMad-Method框架的分片管理和敏捷开发流程。

## 目录结构

### 📚 epics/
存储Epic级别的需求文档，每个Epic代表一个大的功能模块或业务目标。

**文件命名规范：**
- `epic-{编号}-{名称}-v{版本}.md`
- 示例：`epic-001-user-management-v1.0.md`

**内容要求：**
- Epic描述和业务价值
- 相关用户故事列表
- 验收标准和成功指标
- 依赖关系和风险评估

### 📖 stories/
存储从Epic分解出来的详细用户故事文档。

**文件命名规范：**
- `story-{Epic编号}-{故事编号}-{简短描述}.md`
- 示例：`story-001-001-user-registration.md`

**内容要求：**
- 标准用户故事格式（As a... I want... So that...）
- 详细的验收标准
- 工作量估算和优先级
- 相关的UI/UX设计要求

### 📝 requirements/
存储功能需求和非功能需求的详细文档。

**子分类：**
- `functional/` - 功能性需求
- `non-functional/` - 非功能性需求（性能、安全、可用性等）
- `constraints/` - 约束条件和限制

## 使用流程

### 1. 创建PRD文档
```bash
@pm *create
```

### 2. 分片管理
```bash
@pm *shard-prd
```

### 3. Epic创建
```bash
@po *create-epic
```

### 4. 用户故事创建
```bash
@po *create-story
```

## 质量标准

### 文档完整性检查
- [ ] 包含明确的业务价值描述
- [ ] 验收标准具体可测试
- [ ] 工作量估算合理
- [ ] 依赖关系清晰标注

### 审查流程
1. **产品经理审查**：业务价值和需求完整性
2. **技术负责人审查**：技术可行性和架构影响
3. **团队评审**：工作量估算和实施计划

## 模板文件

参考 `bmad-agent/templates/documents/` 目录下的标准模板：
- `prd-template.md` - PRD文档模板
- `epic-template.md` - Epic文档模板
- `story-template.md` - 用户故事模板

## 相关命令

### 常用BMad命令
- `@pm *create` - 创建PRD文档
- `@pm *shard-prd` - PRD文档分片
- `@po *create-epic` - 创建Epic
- `@po *create-story` - 创建用户故事
- `@po *execute-checklist-po` - 执行PO检查清单

### 文档管理命令
- `@bmad-master *doc-out` - 输出完整文档
- `@bmad-master *shard-doc` - 通用文档分片

---

**维护者：** 产品经理 (PM)  
**创建时间：** 2025年8月1日  
**文档状态：** 活跃维护中
