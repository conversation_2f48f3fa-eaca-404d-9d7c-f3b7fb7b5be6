# Yu Reader 核心模块设计文档

## 📋 文档信息

**项目名称：** Yu Reader (玉阅读器)  
**文档版本：** v1.0  
**创建时间：** 2025年8月1日  
**文档类型：** 核心模块设计文档  

## 🎯 模块概述

Yu Reader 采用模块化架构设计，将复杂的功能分解为独立、可维护的模块。每个模块都有明确的职责边界和标准化的接口。

### 核心模块架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Yu Reader 核心模块                        │
├─────────────────────────────────────────────────────────────┤
│  📚 阅读引擎模块 (Reading Engine)                            │
│  ├── 文件解析器 (File Parsers)                              │
│  ├── 内容渲染器 (Content Renderer)                          │
│  ├── 导航控制器 (Navigation Controller)                     │
│  └── 阅读状态管理 (Reading State Manager)                   │
├─────────────────────────────────────────────────────────────┤
│  🤖 AI服务模块 (AI Services)                                │
│  ├── 智能翻译服务 (Translation Service)                     │
│  ├── 内容分析引擎 (Content Analysis Engine)                 │
│  ├── 学习推荐系统 (Learning Recommendation System)          │
│  └── 知识图谱构建器 (Knowledge Graph Builder)               │
├─────────────────────────────────────────────────────────────┤
│  💾 数据管理模块 (Data Management)                          │
│  ├── 数据访问层 (Data Access Layer)                         │
│  ├── 缓存管理器 (Cache Manager)                             │
│  ├── 数据同步服务 (Data Sync Service)                       │
│  └── 备份恢复服务 (Backup & Recovery Service)               │
├─────────────────────────────────────────────────────────────┤
│  📝 学习服务模块 (Learning Services)                        │
│  ├── 词汇管理器 (Vocabulary Manager)                        │
│  ├── 笔记系统 (Note System)                                 │
│  ├── 进度跟踪器 (Progress Tracker)                          │
│  └── 学习分析器 (Learning Analytics)                        │
├─────────────────────────────────────────────────────────────┤
│  🎨 用户界面模块 (UI Modules)                               │
│  ├── 组件库 (Component Library)                             │
│  ├── 主题系统 (Theme System)                                │
│  ├── 布局管理器 (Layout Manager)                            │
│  └── 交互控制器 (Interaction Controller)                    │
├─────────────────────────────────────────────────────────────┤
│  🔧 系统服务模块 (System Services)                          │
│  ├── 配置管理器 (Configuration Manager)                     │
│  ├── 日志服务 (Logging Service)                             │
│  ├── 错误处理器 (Error Handler)                             │
│  └── 性能监控器 (Performance Monitor)                       │
└─────────────────────────────────────────────────────────────┘
```

## 📚 阅读引擎模块

### 模块职责
- 支持多种电子书格式的解析和渲染
- 提供统一的阅读体验接口
- 管理阅读状态和导航
- 优化大文件的加载性能

### 核心组件

#### 1. 文件解析器 (File Parsers)

```typescript
// 解析器接口定义
interface FileParser {
  readonly supportedFormats: string[]
  canParse(filePath: string): boolean
  parse(filePath: string): Promise<ParsedBook>
  extractMetadata(filePath: string): Promise<BookMetadata>
  extractTableOfContents(book: ParsedBook): Promise<TOCItem[]>
}

// EPUB解析器实现
class EPUBParser implements FileParser {
  readonly supportedFormats = ['epub']
  
  async parse(filePath: string): Promise<ParsedBook> {
    const book = ePub(filePath)
    await book.ready
    
    return {
      id: this.generateBookId(filePath),
      metadata: await this.extractBookMetadata(book),
      spine: await this.extractSpine(book),
      resources: await this.extractResources(book),
      navigation: await this.extractNavigation(book)
    }
  }
  
  private async extractBookMetadata(book: any): Promise<BookMetadata> {
    const metadata = book.package.metadata
    return {
      title: metadata.title || '未知标题',
      author: metadata.creator || '未知作者',
      language: metadata.language || 'zh-CN',
      publisher: metadata.publisher,
      publishDate: metadata.date,
      description: metadata.description,
      isbn: metadata.identifier
    }
  }
}

// PDF解析器实现
class PDFParser implements FileParser {
  readonly supportedFormats = ['pdf']
  
  async parse(filePath: string): Promise<ParsedBook> {
    const pdfDoc = await pdfjsLib.getDocument(filePath).promise
    
    return {
      id: this.generateBookId(filePath),
      metadata: await this.extractPDFMetadata(pdfDoc),
      totalPages: pdfDoc.numPages,
      pages: await this.extractPages(pdfDoc)
    }
  }
}

// 解析器工厂
class ParserFactory {
  private parsers: Map<string, FileParser> = new Map()
  
  constructor() {
    this.registerParser(new EPUBParser())
    this.registerParser(new PDFParser())
    this.registerParser(new TXTParser())
    this.registerParser(new DOCXParser())
  }
  
  registerParser(parser: FileParser): void {
    parser.supportedFormats.forEach(format => {
      this.parsers.set(format.toLowerCase(), parser)
    })
  }
  
  getParser(format: string): FileParser | null {
    return this.parsers.get(format.toLowerCase()) || null
  }
  
  getSupportedFormats(): string[] {
    return Array.from(this.parsers.keys())
  }
}
```

#### 2. 内容渲染器 (Content Renderer)

```typescript
// 渲染器接口
interface ContentRenderer {
  render(content: BookContent, options: RenderOptions): Promise<string>
  applyTheme(content: string, theme: Theme): string
  processImages(content: string, baseUrl: string): Promise<string>
  handleInteractiveElements(content: string): string
}

// 通用内容渲染器
class UniversalContentRenderer implements ContentRenderer {
  private themeManager: ThemeManager
  private imageProcessor: ImageProcessor
  
  async render(content: BookContent, options: RenderOptions): Promise<string> {
    let processedContent = content.html
    
    // 应用主题样式
    processedContent = this.applyTheme(processedContent, options.theme)
    
    // 处理图片
    processedContent = await this.processImages(processedContent, content.baseUrl)
    
    // 处理交互元素
    processedContent = this.handleInteractiveElements(processedContent)
    
    // 应用字体和排版设置
    processedContent = this.applyTypography(processedContent, options.typography)
    
    return processedContent
  }
  
  applyTheme(content: string, theme: Theme): string {
    const themeCSS = this.themeManager.generateCSS(theme)
    return `<style>${themeCSS}</style>${content}`
  }
  
  private applyTypography(content: string, typography: TypographySettings): string {
    const typographyCSS = `
      body {
        font-family: ${typography.fontFamily};
        font-size: ${typography.fontSize}px;
        line-height: ${typography.lineHeight};
        letter-spacing: ${typography.letterSpacing}px;
        text-align: ${typography.textAlign};
      }
    `
    return content.replace('<style>', `<style>${typographyCSS}`)
  }
}
```

#### 3. 导航控制器 (Navigation Controller)

```typescript
// 导航控制器
class NavigationController {
  private currentPosition: BookPosition
  private history: BookPosition[] = []
  private bookmarks: Bookmark[] = []
  
  // 导航到指定位置
  async navigateTo(position: BookPosition): Promise<void> {
    this.addToHistory(this.currentPosition)
    this.currentPosition = position
    await this.updateReadingProgress(position)
  }
  
  // 下一页
  async nextPage(): Promise<BookPosition | null> {
    const nextPosition = await this.calculateNextPosition()
    if (nextPosition) {
      await this.navigateTo(nextPosition)
      return nextPosition
    }
    return null
  }
  
  // 上一页
  async previousPage(): Promise<BookPosition | null> {
    const prevPosition = await this.calculatePreviousPosition()
    if (prevPosition) {
      await this.navigateTo(prevPosition)
      return prevPosition
    }
    return null
  }
  
  // 跳转到章节
  async navigateToChapter(chapterIndex: number): Promise<void> {
    const chapterPosition = await this.getChapterPosition(chapterIndex)
    await this.navigateTo(chapterPosition)
  }
  
  // 搜索并导航
  async searchAndNavigate(query: string): Promise<SearchResult[]> {
    const results = await this.searchInBook(query)
    return results
  }
  
  // 添加书签
  async addBookmark(title: string, note?: string): Promise<Bookmark> {
    const bookmark: Bookmark = {
      id: this.generateBookmarkId(),
      title,
      position: this.currentPosition,
      note,
      createdAt: new Date()
    }
    
    this.bookmarks.push(bookmark)
    await this.saveBookmark(bookmark)
    return bookmark
  }
}
```

## 🤖 AI服务模块

### 模块职责
- 提供智能翻译和词典服务
- 分析阅读内容并生成洞察
- 构建个性化学习推荐
- 建立知识图谱关联

### 核心组件

#### 1. 智能翻译服务 (Translation Service)

```typescript
// 翻译服务接口
interface TranslationService {
  translate(text: string, from: string, to: string): Promise<TranslationResult>
  detectLanguage(text: string): Promise<string>
  getWordDefinition(word: string, language: string): Promise<WordDefinition>
  getSynonyms(word: string, language: string): Promise<string[]>
}

// 混合翻译服务实现
class HybridTranslationService implements TranslationService {
  private localService: LocalTranslationService
  private cloudServices: CloudTranslationService[]
  private cache: TranslationCache
  
  constructor() {
    this.localService = new LocalTranslationService()
    this.cloudServices = [
      new GoogleTranslateService(),
      new DeepLTranslateService(),
      new BaiduTranslateService()
    ]
    this.cache = new TranslationCache()
  }
  
  async translate(text: string, from: string, to: string): Promise<TranslationResult> {
    // 检查缓存
    const cacheKey = this.generateCacheKey(text, from, to)
    const cached = await this.cache.get(cacheKey)
    if (cached) {
      return cached
    }
    
    try {
      // 优先使用本地服务
      const result = await this.localService.translate(text, from, to)
      await this.cache.set(cacheKey, result)
      return result
    } catch (error) {
      // 降级到云服务
      return await this.translateWithCloudService(text, from, to)
    }
  }
  
  private async translateWithCloudService(
    text: string, 
    from: string, 
    to: string
  ): Promise<TranslationResult> {
    for (const service of this.cloudServices) {
      try {
        const result = await service.translate(text, from, to)
        const cacheKey = this.generateCacheKey(text, from, to)
        await this.cache.set(cacheKey, result)
        return result
      } catch (error) {
        console.warn(`翻译服务 ${service.name} 失败:`, error)
        continue
      }
    }
    
    throw new Error('所有翻译服务都不可用')
  }
}
```

#### 2. 内容分析引擎 (Content Analysis Engine)

```typescript
// 内容分析引擎
class ContentAnalysisEngine {
  private nlpProcessor: NLPProcessor
  private sentimentAnalyzer: SentimentAnalyzer
  private topicExtractor: TopicExtractor
  private difficultyAssessor: DifficultyAssessor
  
  // 分析文本内容
  async analyzeContent(content: string): Promise<ContentAnalysis> {
    const [
      entities,
      sentiment,
      topics,
      difficulty,
      readingTime,
      keyPhrases
    ] = await Promise.all([
      this.nlpProcessor.extractEntities(content),
      this.sentimentAnalyzer.analyze(content),
      this.topicExtractor.extract(content),
      this.difficultyAssessor.assess(content),
      this.estimateReadingTime(content),
      this.nlpProcessor.extractKeyPhrases(content)
    ])
    
    return {
      entities,
      sentiment,
      topics,
      difficulty,
      readingTime,
      keyPhrases,
      wordCount: this.countWords(content),
      languageLevel: this.assessLanguageLevel(content)
    }
  }
  
  // 生成内容摘要
  async generateSummary(content: string, maxLength: number = 200): Promise<string> {
    const sentences = this.splitIntoSentences(content)
    const scoredSentences = await this.scoreSentences(sentences)
    const selectedSentences = this.selectTopSentences(scoredSentences, maxLength)
    return selectedSentences.join(' ')
  }
  
  // 生成阅读问题
  async generateQuestions(content: string): Promise<Question[]> {
    const analysis = await this.analyzeContent(content)
    const questions: Question[] = []
    
    // 基于实体生成问题
    for (const entity of analysis.entities) {
      if (entity.type === 'PERSON' || entity.type === 'ORGANIZATION') {
        questions.push({
          type: 'factual',
          question: `谁是${entity.text}？`,
          context: entity.context
        })
      }
    }
    
    // 基于主题生成问题
    for (const topic of analysis.topics) {
      questions.push({
        type: 'conceptual',
        question: `请解释${topic.name}的概念`,
        context: topic.context
      })
    }
    
    return questions
  }
}
```

## 💾 数据管理模块

### 模块职责
- 提供统一的数据访问接口
- 管理数据缓存和同步
- 处理数据备份和恢复
- 确保数据一致性和完整性

### 核心组件

#### 1. 数据访问层 (Data Access Layer)

```typescript
// 仓储模式基类
abstract class BaseRepository<T, ID> {
  protected db: Database
  protected tableName: string
  
  constructor(db: Database, tableName: string) {
    this.db = db
    this.tableName = tableName
  }
  
  abstract findById(id: ID): Promise<T | null>
  abstract findAll(): Promise<T[]>
  abstract create(entity: Omit<T, 'id'>): Promise<T>
  abstract update(id: ID, entity: Partial<T>): Promise<T>
  abstract delete(id: ID): Promise<void>
  
  // 通用查询方法
  protected async query(sql: string, params: any[] = []): Promise<any[]> {
    const stmt = this.db.prepare(sql)
    return stmt.all(...params)
  }
  
  protected async queryOne(sql: string, params: any[] = []): Promise<any | null> {
    const stmt = this.db.prepare(sql)
    return stmt.get(...params) || null
  }
  
  protected async execute(sql: string, params: any[] = []): Promise<any> {
    const stmt = this.db.prepare(sql)
    return stmt.run(...params)
  }
}

// 图书仓储实现
class BookRepository extends BaseRepository<Book, number> {
  constructor(db: Database) {
    super(db, 'books')
  }
  
  async findById(id: number): Promise<Book | null> {
    const result = await this.queryOne('SELECT * FROM books WHERE id = ?', [id])
    return result ? this.mapToEntity(result) : null
  }
  
  async findAll(): Promise<Book[]> {
    const results = await this.query('SELECT * FROM books ORDER BY updated_at DESC')
    return results.map(result => this.mapToEntity(result))
  }
  
  async create(bookData: Omit<Book, 'id'>): Promise<Book> {
    const result = await this.execute(`
      INSERT INTO books (title, author, format, file_path, created_at, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [bookData.title, bookData.author, bookData.format, bookData.file_path])
    
    return await this.findById(result.lastInsertRowid)!
  }
  
  async searchByTitle(query: string): Promise<Book[]> {
    const results = await this.query(
      'SELECT * FROM books WHERE title LIKE ? ORDER BY title',
      [`%${query}%`]
    )
    return results.map(result => this.mapToEntity(result))
  }
  
  private mapToEntity(row: any): Book {
    return {
      id: row.id,
      title: row.title,
      author: row.author,
      format: row.format,
      file_path: row.file_path,
      created_at: row.created_at,
      updated_at: row.updated_at,
      // 解析JSON字段
      tags: row.tags ? JSON.parse(row.tags) : [],
      metadata: row.metadata ? JSON.parse(row.metadata) : {}
    }
  }
}
```

#### 2. 缓存管理器 (Cache Manager)

```typescript
// 缓存管理器
class CacheManager {
  private memoryCache: Map<string, CacheItem> = new Map()
  private diskCache: DiskCache
  private maxMemorySize: number = 100 * 1024 * 1024 // 100MB
  private currentMemorySize: number = 0
  
  constructor() {
    this.diskCache = new DiskCache()
    this.startCleanupTimer()
  }
  
  // 获取缓存项
  async get<T>(key: string): Promise<T | null> {
    // 先检查内存缓存
    const memoryItem = this.memoryCache.get(key)
    if (memoryItem && !this.isExpired(memoryItem)) {
      return memoryItem.data as T
    }
    
    // 检查磁盘缓存
    const diskItem = await this.diskCache.get(key)
    if (diskItem && !this.isExpired(diskItem)) {
      // 将热数据加载到内存
      this.setMemoryCache(key, diskItem.data, diskItem.ttl)
      return diskItem.data as T
    }
    
    return null
  }
  
  // 设置缓存项
  async set<T>(key: string, data: T, ttl: number = 3600): Promise<void> {
    const item: CacheItem = {
      data,
      ttl,
      createdAt: Date.now(),
      size: this.calculateSize(data)
    }
    
    // 设置内存缓存
    this.setMemoryCache(key, data, ttl)
    
    // 设置磁盘缓存
    await this.diskCache.set(key, item)
  }
  
  private setMemoryCache<T>(key: string, data: T, ttl: number): void {
    const item: CacheItem = {
      data,
      ttl,
      createdAt: Date.now(),
      size: this.calculateSize(data)
    }
    
    // 检查内存限制
    if (this.currentMemorySize + item.size > this.maxMemorySize) {
      this.evictLRU()
    }
    
    this.memoryCache.set(key, item)
    this.currentMemorySize += item.size
  }
  
  // LRU淘汰策略
  private evictLRU(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.createdAt < oldestTime) {
        oldestTime = item.createdAt
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      const item = this.memoryCache.get(oldestKey)!
      this.memoryCache.delete(oldestKey)
      this.currentMemorySize -= item.size
    }
  }
}
```

## 📝 学习服务模块

### 模块职责
- 管理用户词汇学习
- 提供智能笔记功能
- 跟踪学习进度
- 生成学习分析报告

### 核心组件

#### 1. 词汇管理器 (Vocabulary Manager)

```typescript
// 词汇管理器
class VocabularyManager {
  private wordRepository: WordRepository
  private spacedRepetitionAlgorithm: SpacedRepetitionAlgorithm
  
  // 添加新单词
  async addWord(word: string, translation: string, context: string, bookId: number): Promise<VocabularyWord> {
    const existingWord = await this.wordRepository.findByWord(word)
    
    if (existingWord) {
      // 更新上下文和出现次数
      return await this.wordRepository.update(existingWord.id, {
        contexts: [...existingWord.contexts, context],
        occurrenceCount: existingWord.occurrenceCount + 1,
        lastEncountered: new Date()
      })
    } else {
      // 创建新单词记录
      return await this.wordRepository.create({
        word,
        translation,
        contexts: [context],
        bookId,
        masteryLevel: 0,
        occurrenceCount: 1,
        reviewCount: 0,
        lastEncountered: new Date(),
        nextReviewDate: this.spacedRepetitionAlgorithm.calculateNextReview(0)
      })
    }
  }
  
  // 获取需要复习的单词
  async getWordsForReview(limit: number = 20): Promise<VocabularyWord[]> {
    return await this.wordRepository.findWordsForReview(new Date(), limit)
  }
  
  // 更新单词掌握程度
  async updateWordMastery(wordId: number, correct: boolean): Promise<VocabularyWord> {
    const word = await this.wordRepository.findById(wordId)
    if (!word) {
      throw new Error('单词不存在')
    }
    
    const newMasteryLevel = this.spacedRepetitionAlgorithm.updateMastery(
      word.masteryLevel,
      correct
    )
    
    const nextReviewDate = this.spacedRepetitionAlgorithm.calculateNextReview(
      newMasteryLevel,
      word.reviewCount + 1
    )
    
    return await this.wordRepository.update(wordId, {
      masteryLevel: newMasteryLevel,
      reviewCount: word.reviewCount + 1,
      lastReviewed: new Date(),
      nextReviewDate
    })
  }
  
  // 生成词汇学习报告
  async generateLearningReport(userId: number, period: 'week' | 'month' | 'year'): Promise<VocabularyReport> {
    const stats = await this.wordRepository.getStatistics(userId, period)
    
    return {
      totalWords: stats.totalWords,
      newWords: stats.newWords,
      reviewedWords: stats.reviewedWords,
      masteredWords: stats.masteredWords,
      accuracyRate: stats.correctReviews / stats.totalReviews,
      averageMasteryLevel: stats.averageMasteryLevel,
      dailyProgress: stats.dailyProgress,
      weakWords: await this.wordRepository.findWeakWords(userId, 10)
    }
  }
}
```

## 🎨 用户界面模块

### 模块职责
- 提供可复用的UI组件
- 管理应用主题和样式
- 处理用户交互逻辑
- 优化界面性能

### 核心组件

#### 1. 主题系统 (Theme System)

```typescript
// 主题管理器
class ThemeManager {
  private currentTheme: Theme
  private themes: Map<string, Theme> = new Map()
  private cssVariables: Map<string, string> = new Map()
  
  constructor() {
    this.loadBuiltinThemes()
    this.currentTheme = this.themes.get('default')!
  }
  
  // 加载内置主题
  private loadBuiltinThemes(): void {
    const lightTheme: Theme = {
      id: 'light',
      name: '浅色主题',
      colors: {
        primary: '#1976d2',
        secondary: '#424242',
        background: '#ffffff',
        surface: '#f5f5f5',
        text: '#212121',
        textSecondary: '#757575'
      },
      typography: {
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fontSize: {
          small: 12,
          medium: 14,
          large: 16,
          xlarge: 18
        }
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32
      }
    }
    
    const darkTheme: Theme = {
      ...lightTheme,
      id: 'dark',
      name: '深色主题',
      colors: {
        primary: '#90caf9',
        secondary: '#f48fb1',
        background: '#121212',
        surface: '#1e1e1e',
        text: '#ffffff',
        textSecondary: '#b0b0b0'
      }
    }
    
    this.themes.set('light', lightTheme)
    this.themes.set('dark', darkTheme)
  }
  
  // 应用主题
  applyTheme(themeId: string): void {
    const theme = this.themes.get(themeId)
    if (!theme) {
      throw new Error(`主题 ${themeId} 不存在`)
    }
    
    this.currentTheme = theme
    this.updateCSSVariables(theme)
    this.notifyThemeChange(theme)
  }
  
  // 更新CSS变量
  private updateCSSVariables(theme: Theme): void {
    const root = document.documentElement
    
    // 颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    
    // 字体变量
    root.style.setProperty('--font-family', theme.typography.fontFamily)
    Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, `${value}px`)
    })
    
    // 间距变量
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, `${value}px`)
    })
  }
  
  // 创建自定义主题
  createCustomTheme(baseThemeId: string, customizations: Partial<Theme>): Theme {
    const baseTheme = this.themes.get(baseThemeId)
    if (!baseTheme) {
      throw new Error(`基础主题 ${baseThemeId} 不存在`)
    }
    
    const customTheme: Theme = {
      ...baseTheme,
      ...customizations,
      id: customizations.id || `custom-${Date.now()}`,
      colors: { ...baseTheme.colors, ...customizations.colors },
      typography: { ...baseTheme.typography, ...customizations.typography },
      spacing: { ...baseTheme.spacing, ...customizations.spacing }
    }
    
    this.themes.set(customTheme.id, customTheme)
    return customTheme
  }
}
```

## 🔧 系统服务模块

### 模块职责
- 管理应用配置
- 提供日志记录服务
- 处理错误和异常
- 监控应用性能

### 核心组件

#### 1. 配置管理器 (Configuration Manager)

```typescript
// 配置管理器
class ConfigurationManager {
  private config: AppConfig
  private configPath: string
  private watchers: ConfigWatcher[] = []
  
  constructor() {
    this.configPath = this.getConfigPath()
    this.loadConfig()
    this.watchConfigFile()
  }
  
  // 获取配置值
  get<T>(key: string, defaultValue?: T): T {
    const keys = key.split('.')
    let value: any = this.config
    
    for (const k of keys) {
      value = value?.[k]
      if (value === undefined) {
        return defaultValue as T
      }
    }
    
    return value as T
  }
  
  // 设置配置值
  set(key: string, value: any): void {
    const keys = key.split('.')
    let target: any = this.config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]
      if (!(k in target)) {
        target[k] = {}
      }
      target = target[k]
    }
    
    target[keys[keys.length - 1]] = value
    this.saveConfig()
    this.notifyWatchers(key, value)
  }
  
  // 监听配置变化
  watch(key: string, callback: (value: any) => void): () => void {
    const watcher: ConfigWatcher = { key, callback }
    this.watchers.push(watcher)
    
    // 返回取消监听的函数
    return () => {
      const index = this.watchers.indexOf(watcher)
      if (index > -1) {
        this.watchers.splice(index, 1)
      }
    }
  }
  
  // 重置配置
  reset(): void {
    this.config = this.getDefaultConfig()
    this.saveConfig()
  }
  
  private getDefaultConfig(): AppConfig {
    return {
      app: {
        language: 'zh-CN',
        theme: 'light',
        autoSave: true,
        checkUpdates: true
      },
      reading: {
        fontSize: 16,
        lineHeight: 1.6,
        fontFamily: 'system',
        pageWidth: 800,
        pageMargin: 40
      },
      learning: {
        enableTranslation: true,
        translationService: 'google',
        autoSaveWords: true,
        reviewReminder: true
      },
      performance: {
        enableCache: true,
        cacheSize: 100,
        preloadPages: 3
      }
    }
  }
}
```

---

**文档维护**: 本文档将随着模块实现的进展持续更新，确保设计文档与实际代码保持一致。
