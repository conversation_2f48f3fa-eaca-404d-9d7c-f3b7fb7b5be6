# Yu Reader 项目初始化与环境搭建指南

## 📋 文档信息

**项目名称：** Yu Reader (玉阅读器)  
**文档版本：** v1.0  
**创建时间：** 2025年8月1日  
**文档类型：** 开发环境搭建指南  

## 🎯 环境要求

### 系统要求
- **操作系统**：Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**：18.0+ (推荐 LTS 版本)
- **npm**：9.0+ 或 yarn 1.22+
- **Git**：2.30+
- **内存**：至少 8GB RAM (推荐 16GB)
- **存储**：至少 5GB 可用空间

### 开发工具推荐
- **IDE**：VS Code (推荐) 或 WebStorm
- **终端**：Windows Terminal, iTerm2, 或系统默认终端
- **浏览器**：Chrome 或 Edge (用于调试)

## 🚀 快速开始

### 1. 克隆项目
```bash
# 克隆仓库
git clone https://github.com/your-org/yu-reader.git
cd yu-reader

# 检查 Node.js 版本
node --version  # 应该是 18.0+
npm --version   # 应该是 9.0+
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 或使用 yarn
yarn install
```

### 3. 启动开发环境
```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev
```

### 4. 验证安装
- 应用应该在几秒钟内启动
- 会打开一个 Electron 窗口显示 Yu Reader 界面
- 控制台应该没有错误信息

## 📁 项目结构

```
yu-reader/
├── src/                          # 源代码目录
│   ├── main/                     # Electron 主进程
│   │   ├── index.ts              # 主进程入口
│   │   ├── window.ts             # 窗口管理
│   │   ├── menu.ts               # 应用菜单
│   │   ├── ipc/                  # IPC 通信处理
│   │   ├── services/             # 主进程服务
│   │   └── database/             # 数据库相关
│   ├── renderer/                 # Vue3 渲染进程
│   │   ├── src/                  # Vue 应用源码
│   │   │   ├── components/       # Vue 组件
│   │   │   ├── views/            # 页面视图
│   │   │   ├── stores/           # Pinia 状态管理
│   │   │   ├── services/         # 前端服务
│   │   │   ├── utils/            # 工具函数
│   │   │   ├── types/            # TypeScript 类型
│   │   │   ├── assets/           # 静态资源
│   │   │   └── styles/           # 样式文件
│   │   ├── index.html            # HTML 模板
│   │   └── vite.config.ts        # Vite 配置
│   ├── shared/                   # 共享代码
│   │   ├── types/                # 共享类型定义
│   │   ├── constants/            # 常量定义
│   │   └── utils/                # 共享工具函数
│   └── preload/                  # 预加载脚本
│       └── index.ts              # 预加载脚本入口
├── tests/                        # 测试文件
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   └── e2e/                      # 端到端测试
├── docs/                         # 项目文档
│   ├── architecture/             # 架构文档
│   ├── development/              # 开发文档
│   └── api/                      # API 文档
├── resources/                    # 资源文件
│   ├── icons/                    # 应用图标
│   ├── images/                   # 图片资源
│   └── fonts/                    # 字体文件
├── build/                        # 构建配置
│   ├── icons/                    # 构建用图标
│   └── scripts/                  # 构建脚本
├── dist/                         # 构建输出 (git ignore)
├── node_modules/                 # 依赖包 (git ignore)
├── package.json                  # 项目配置
├── package-lock.json             # 依赖锁定
├── tsconfig.json                 # TypeScript 配置
├── electron.vite.config.ts       # Electron Vite 配置
├── electron-builder.yml          # 打包配置
├── .eslintrc.js                  # ESLint 配置
├── .prettierrc                   # Prettier 配置
├── .gitignore                    # Git 忽略文件
└── README.md                     # 项目说明
```

## ⚙️ 配置文件详解

### package.json
```json
{
  "name": "yu-reader",
  "version": "1.0.0",
  "description": "现代化的跨平台桌面电子书阅读器",
  "main": "dist/main/index.js",
  "author": "Yu Reader Team",
  "license": "MIT",
  "private": true,
  "scripts": {
    "dev": "electron-vite dev",
    "build": "electron-vite build",
    "preview": "electron-vite preview",
    "package": "electron-builder",
    "package:win": "electron-builder --win",
    "package:mac": "electron-builder --mac",
    "package:linux": "electron-builder --linux",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .ts,.vue --fix",
    "format": "prettier --write .",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "electron": "^28.0.0",
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "sqlite3": "^5.1.0",
    "epub": "^1.2.0",
    "pdfjs-dist": "^3.11.0",
    "mammoth": "^1.6.0",
    "jszip": "^3.10.0",
    "fuse.js": "^7.0.0"
  },
  "devDependencies": {
    "electron-vite": "^2.0.0",
    "electron-builder": "^24.0.0",
    "typescript": "^5.0.0",
    "vue-tsc": "^1.8.0",
    "vite": "^5.0.0",
    "vitest": "^1.0.0",
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/test-utils": "^2.4.0",
    "playwright": "^1.40.0",
    "eslint": "^8.55.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "eslint-plugin-vue": "^9.19.0",
    "prettier": "^3.1.0",
    "unplugin-auto-import": "^0.17.0",
    "unplugin-vue-components": "^0.26.0"
  }
}
```

### electron.vite.config.ts
```typescript
import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        external: ['sqlite3', 'epub', 'pdfjs-dist']
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    resolve: {
      alias: {
        '@': resolve('src/renderer/src'),
        '@shared': resolve('src/shared')
      }
    },
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia']
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      })
    ]
  }
})
```

### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/renderer/src/*"],
      "@shared/*": ["src/shared/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": ["node_modules", "dist"]
}
```

## 🛠️ 开发工具配置

### VS Code 推荐扩展
创建 `.vscode/extensions.json`:
```json
{
  "recommendations": [
    "vue.volar",
    "vue.typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### VS Code 工作区设置
创建 `.vscode/settings.json`:
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "kebab",
  "vue.complete.casing.props": "camel"
}
```

## 🔧 开发命令

### 基础开发命令
```bash
# 启动开发环境
npm run dev

# 构建项目
npm run build

# 预览构建结果
npm run preview

# 打包应用
npm run package

# 特定平台打包
npm run package:win    # Windows
npm run package:mac    # macOS
npm run package:linux  # Linux
```

### 测试命令
```bash
# 运行单元测试
npm run test

# 运行测试并显示 UI
npm run test:ui

# 运行端到端测试
npm run test:e2e

# 运行测试覆盖率
npm run test:coverage
```

### 代码质量命令
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# TypeScript 类型检查
npm run type-check
```

## 🗄️ 数据库初始化

### SQLite 数据库设置
```typescript
// src/main/database/init.ts
import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'

export function initDatabase(): Database.Database {
  const dbPath = join(app.getPath('userData'), 'yu-reader.db')
  const db = new Database(dbPath)
  
  // 创建表结构
  createTables(db)
  
  return db
}

function createTables(db: Database.Database): void {
  // 图书表
  db.exec(`
    CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      author TEXT,
      isbn TEXT,
      format TEXT NOT NULL,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      cover_path TEXT,
      language TEXT,
      publisher TEXT,
      publish_date DATE,
      description TEXT,
      tags TEXT,
      metadata TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)
  
  // 其他表...
}
```

## 🎨 主题和样式配置

### Element Plus 主题定制
```scss
// src/renderer/src/styles/element-variables.scss
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #1976d2,
    ),
    'success': (
      'base': #4caf50,
    ),
    'warning': (
      'base': #ff9800,
    ),
    'danger': (
      'base': #f44336,
    ),
  ),
  $border-radius: (
    'base': 8px,
  )
);
```

### 全局样式
```scss
// src/renderer/src/styles/global.scss
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden;
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
```

## 🚨 常见问题解决

### 1. Node.js 版本问题
```bash
# 使用 nvm 管理 Node.js 版本
nvm install 18
nvm use 18
```

### 2. 依赖安装失败
```bash
# 清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. Electron 下载慢
```bash
# 设置 Electron 镜像
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
```

### 4. SQLite3 编译问题
```bash
# 重新编译 SQLite3
npm rebuild sqlite3
```

## 📝 开发规范

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

示例：
feat: 添加EPUB文件解析功能
fix: 修复书签保存失败的问题
docs: 更新API文档
```

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 常量使用 UPPER_SNAKE_CASE

## 🔄 下一步

环境搭建完成后，建议按以下顺序进行开发：

1. **熟悉项目结构**：浏览各个目录和文件
2. **运行示例代码**：确保基础功能正常
3. **阅读技术文档**：了解架构设计和API规范
4. **开始功能开发**：从简单功能开始实现
5. **编写测试用例**：确保代码质量

---

**注意**：如果在环境搭建过程中遇到问题，请查看项目的 Issue 页面或联系开发团队。
