# Yu Reader (玉阅读器)

<div align="center">

![Yu Reader Logo](resources/images/logo.png)

**现代化的跨平台桌面电子书阅读器**

专注于学习场景的智能化阅读体验

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Electron](https://img.shields.io/badge/Electron-28+-blue.svg)](https://electronjs.org/)
[![Vue](https://img.shields.io/badge/Vue-3.4+-green.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [技术架构](#-技术架构) • [开发指南](#-开发指南) • [贡献指南](#-贡献指南)

</div>

## 🎯 项目概述

Yu Reader 是一个现代化的跨平台桌面电子书阅读器，专为学习场景设计。它不仅提供优秀的阅读体验，更集成了强大的AI学习辅助功能，帮助用户更高效地学习和理解内容。

### 🌟 核心亮点

- **🤖 智能阅读伴侣**：AI驱动的内容分析和学习建议
- **📚 多格式支持**：EPUB、PDF、TXT、MOBI、AZW3等主流格式
- **🧠 自适应学习**：个性化学习路径和难度调整
- **🔍 智能翻译**：划词翻译、生词本、间隔重复学习
- **📊 学习分析**：详细的阅读统计和学习效果分析
- **🌐 知识图谱**：智能构建概念关联和知识网络
- **👥 协作学习**：笔记分享、讨论区、学习小组
- **🎨 现代界面**：简洁美观的用户界面，支持多主题

## ✨ 功能特性

### 📖 核心阅读功能
- **多格式支持**：支持EPUB、PDF、TXT、MOBI、AZW3、DOCX等格式
- **智能书架**：自动分类、标签管理、搜索筛选
- **阅读体验**：自定义字体、主题、排版，护眼模式
- **导航控制**：目录导航、书签管理、阅读进度同步

### 🤖 AI智能功能
- **智能阅读伴侣**：内容分析、难度评估、阅读建议
- **自适应学习引擎**：个性化推荐、学习路径规划
- **多模态理解**：图文音频内容智能处理
- **知识图谱构建**：概念关联、知识网络可视化

### 📝 学习辅助功能
- **划词翻译**：支持多种翻译服务，离线词典
- **生词本管理**：智能收集、间隔重复学习
- **笔记系统**：高亮标注、富文本笔记、标签分类
- **学习统计**：阅读时间、进度跟踪、效果分析

### 🌐 协作与社交
- **笔记分享**：与他人分享学习笔记和心得
- **学习小组**：创建或加入学习小组
- **讨论区**：针对内容进行讨论和交流
- **专家问答**：获得专业指导和建议

### 🔮 创新体验功能
- **学习DNA分析**：深度分析学习行为和认知模式
- **预测性分析**：学习趋势预测、困难点识别
- **跨平台同步**：多设备学习数据同步
- **学习社区**：构建完整的学习生态系统

## 🚀 快速开始

### 系统要求
- **操作系统**：Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**：至少 4GB RAM (推荐 8GB)
- **存储**：至少 500MB 可用空间
- **网络**：可选，用于AI功能和云同步

### 安装方式

#### 方式一：下载预编译版本
1. 访问 [Releases 页面](https://github.com/your-org/yu-reader/releases)
2. 下载适合您系统的安装包
3. 运行安装程序并按提示完成安装

#### 方式二：从源码构建
```bash
# 克隆仓库
git clone https://github.com/your-org/yu-reader.git
cd yu-reader

# 安装依赖
npm install

# 启动开发环境
npm run dev

# 构建应用
npm run build

# 打包应用
npm run package
```

### 首次使用
1. 启动 Yu Reader
2. 导入您的电子书文件（支持拖拽导入）
3. 开始阅读并体验智能学习功能
4. 根据需要配置个人偏好设置

## 🏗️ 技术架构

### 技术栈
- **前端框架**：Vue 3 + TypeScript + Element Plus
- **桌面框架**：Electron 28+
- **数据库**：SQLite3 + IndexedDB
- **AI服务**：TensorFlow.js + 云端API
- **构建工具**：Vite + Electron Builder
- **测试框架**：Vitest + Playwright

### 架构设计
```
┌─────────────────────────────────────────┐
│              Yu Reader                  │
├─────────────────────────────────────────┤
│  📚 阅读引擎  │  🤖 AI服务  │  💾 数据管理  │
│  📝 学习服务  │  🎨 UI模块  │  🔧 系统服务  │
└─────────────────────────────────────────┘
```

详细的技术架构请参考：[技术架构文档](docs/architecture/yu-reader-technical-architecture.md)

## 📚 文档

### 用户文档
- [用户使用手册](docs/user/user-guide.md)
- [功能介绍](docs/user/features.md)
- [常见问题](docs/user/faq.md)

### 开发文档
- [开发环境搭建](docs/development/project-setup-guide.md)
- [技术架构设计](docs/architecture/yu-reader-technical-architecture.md)
- [核心模块设计](docs/architecture/core-modules-design.md)
- [API文档](docs/api/api-reference.md)

### 项目文档
- [产品需求文档](docs/prd/yu-reader-prd-v3.md)
- [实施计划](docs/project/implementation-plan.md)
- [测试计划](docs/testing/test-plan.md)

## 🛠️ 开发指南

### 开发环境设置
```bash
# 安装 Node.js 18+
node --version

# 克隆项目
git clone https://github.com/your-org/yu-reader.git
cd yu-reader

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 项目结构
```
yu-reader/
├── src/
│   ├── main/           # Electron 主进程
│   ├── renderer/       # Vue3 渲染进程
│   ├── shared/         # 共享代码
│   └── preload/        # 预加载脚本
├── docs/               # 项目文档
├── tests/              # 测试文件
├── resources/          # 资源文件
└── build/              # 构建配置
```

### 开发规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 代码规范
- 编写单元测试和集成测试
- 提交前运行完整的测试套件

### 常用命令
```bash
npm run dev          # 启动开发环境
npm run build        # 构建项目
npm run test         # 运行测试
npm run lint         # 代码检查
npm run format       # 代码格式化
npm run package      # 打包应用
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 贡献方式
- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- 🌍 帮助翻译

### 开发流程
1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为 Yu Reader 项目做出贡献的开发者和用户！

特别感谢以下开源项目：
- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [epub.js](https://github.com/futurepress/epub.js/) - EPUB解析库
- [PDF.js](https://mozilla.github.io/pdf.js/) - PDF解析库

## 📞 联系我们

- **项目主页**：https://github.com/your-org/yu-reader
- **问题反馈**：https://github.com/your-org/yu-reader/issues
- **讨论区**：https://github.com/your-org/yu-reader/discussions
- **邮箱**：<EMAIL>

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

Made with ❤️ by Yu Reader Team

</div>
