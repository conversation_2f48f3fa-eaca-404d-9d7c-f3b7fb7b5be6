import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'

/**
 * 数据库管理模块
 * 负责SQLite数据库的初始化、连接和基础操作
 */

let db: Database.Database | null = null

/**
 * 获取数据库文件路径
 */
function getDatabasePath(): string {
  const userDataPath = app.getPath('userData')
  
  // 确保用户数据目录存在
  if (!existsSync(userDataPath)) {
    mkdirSync(userDataPath, { recursive: true })
  }
  
  return join(userDataPath, 'yu-reader.db')
}

/**
 * 初始化数据库
 */
export async function initDatabase(): Promise<Database.Database> {
  if (db) {
    return db
  }

  const dbPath = getDatabasePath()
  console.log('数据库路径:', dbPath)

  try {
    // 创建数据库连接
    db = new Database(dbPath)
    
    // 设置数据库配置
    db.pragma('journal_mode = WAL') // 使用WAL模式提高并发性能
    db.pragma('synchronous = NORMAL') // 平衡性能和安全性
    db.pragma('cache_size = 1000') // 设置缓存大小
    db.pragma('temp_store = memory') // 临时表存储在内存中
    db.pragma('foreign_keys = ON') // 启用外键约束

    // 创建表结构
    await createTables(db)
    
    // 创建索引
    await createIndexes(db)
    
    console.log('数据库初始化完成')
    return db
  } catch (error) {
    console.error('数据库初始化失败:', error)
    throw error
  }
}

/**
 * 获取数据库实例
 */
export function getDatabase(): Database.Database {
  if (!db) {
    throw new Error('数据库未初始化，请先调用 initDatabase()')
  }
  return db
}

/**
 * 关闭数据库连接
 */
export function closeDatabase(): void {
  if (db) {
    db.close()
    db = null
    console.log('数据库连接已关闭')
  }
}

/**
 * 创建数据表
 */
async function createTables(database: Database.Database): Promise<void> {
  const tables = [
    // 图书信息表
    `CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      author TEXT,
      isbn TEXT,
      format TEXT NOT NULL,
      file_path TEXT NOT NULL UNIQUE,
      file_size INTEGER,
      cover_path TEXT,
      language TEXT,
      publisher TEXT,
      publish_date DATE,
      description TEXT,
      tags TEXT, -- JSON array
      metadata TEXT, -- JSON object
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // 阅读进度表
    `CREATE TABLE IF NOT EXISTS reading_progress (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      current_position TEXT NOT NULL, -- JSON object with format-specific position
      progress_percentage REAL DEFAULT 0,
      reading_time INTEGER DEFAULT 0, -- seconds
      last_read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )`,

    // 书签表
    `CREATE TABLE IF NOT EXISTS bookmarks (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      title TEXT,
      position TEXT NOT NULL, -- JSON object
      content_preview TEXT,
      color TEXT DEFAULT '#ffeb3b',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )`,

    // 笔记表
    `CREATE TABLE IF NOT EXISTS notes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      content TEXT NOT NULL,
      position TEXT NOT NULL, -- JSON object
      highlight_text TEXT,
      note_type TEXT DEFAULT 'text', -- text, voice, image
      tags TEXT, -- JSON array
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )`,

    // 学习记录表
    `CREATE TABLE IF NOT EXISTS learning_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      word TEXT NOT NULL,
      translation TEXT,
      context TEXT,
      position TEXT NOT NULL, -- JSON object
      mastery_level INTEGER DEFAULT 0, -- 0-5
      review_count INTEGER DEFAULT 0,
      last_reviewed_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )`,

    // 用户设置表
    `CREATE TABLE IF NOT EXISTS user_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT NOT NULL UNIQUE,
      value TEXT NOT NULL,
      type TEXT DEFAULT 'string', -- string, number, boolean, json
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // 阅读统计表
    `CREATE TABLE IF NOT EXISTS reading_statistics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      date DATE NOT NULL,
      reading_time INTEGER DEFAULT 0, -- seconds
      pages_read INTEGER DEFAULT 0,
      words_read INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
      UNIQUE(book_id, date)
    )`
  ]

  // 执行创建表的SQL语句
  for (const sql of tables) {
    try {
      database.exec(sql)
    } catch (error) {
      console.error('创建表失败:', error)
      throw error
    }
  }

  console.log('数据表创建完成')
}

/**
 * 创建索引
 */
async function createIndexes(database: Database.Database): Promise<void> {
  const indexes = [
    // 图书表索引
    'CREATE INDEX IF NOT EXISTS idx_books_title ON books(title)',
    'CREATE INDEX IF NOT EXISTS idx_books_author ON books(author)',
    'CREATE INDEX IF NOT EXISTS idx_books_format ON books(format)',
    'CREATE INDEX IF NOT EXISTS idx_books_created_at ON books(created_at)',

    // 阅读进度表索引
    'CREATE INDEX IF NOT EXISTS idx_reading_progress_book_id ON reading_progress(book_id)',
    'CREATE INDEX IF NOT EXISTS idx_reading_progress_last_read ON reading_progress(last_read_at)',

    // 书签表索引
    'CREATE INDEX IF NOT EXISTS idx_bookmarks_book_id ON bookmarks(book_id)',
    'CREATE INDEX IF NOT EXISTS idx_bookmarks_created_at ON bookmarks(created_at)',

    // 笔记表索引
    'CREATE INDEX IF NOT EXISTS idx_notes_book_id ON notes(book_id)',
    'CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at)',

    // 学习记录表索引
    'CREATE INDEX IF NOT EXISTS idx_learning_records_book_id ON learning_records(book_id)',
    'CREATE INDEX IF NOT EXISTS idx_learning_records_word ON learning_records(word)',
    'CREATE INDEX IF NOT EXISTS idx_learning_records_mastery ON learning_records(mastery_level)',

    // 用户设置表索引
    'CREATE INDEX IF NOT EXISTS idx_user_settings_key ON user_settings(key)',

    // 阅读统计表索引
    'CREATE INDEX IF NOT EXISTS idx_reading_statistics_book_date ON reading_statistics(book_id, date)',
    'CREATE INDEX IF NOT EXISTS idx_reading_statistics_date ON reading_statistics(date)'
  ]

  // 执行创建索引的SQL语句
  for (const sql of indexes) {
    try {
      database.exec(sql)
    } catch (error) {
      console.error('创建索引失败:', error)
      throw error
    }
  }

  console.log('数据库索引创建完成')
}

/**
 * 数据库健康检查
 */
export function checkDatabaseHealth(): boolean {
  try {
    if (!db) {
      return false
    }

    // 执行简单查询测试连接
    const result = db.prepare('SELECT 1 as test').get()
    return result && result.test === 1
  } catch (error) {
    console.error('数据库健康检查失败:', error)
    return false
  }
}

/**
 * 备份数据库
 */
export async function backupDatabase(backupPath: string): Promise<void> {
  if (!db) {
    throw new Error('数据库未初始化')
  }

  try {
    await db.backup(backupPath)
    console.log('数据库备份完成:', backupPath)
  } catch (error) {
    console.error('数据库备份失败:', error)
    throw error
  }
}
