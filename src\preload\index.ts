import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron'
import type { 
  Book, 
  CreateBookDto, 
  UpdateBookDto, 
  BookSearchOptions,
  BookSearchResult,
  BookStats,
  BookImportResult,
  BookImportOptions
} from '@shared/types/book'

/**
 * 预加载脚本
 * 负责在渲染进程中暴露安全的Electron API
 */

// 定义API接口
export interface ElectronAPI {
  // 应用信息
  app: {
    getVersion(): Promise<string>
    getName(): Promise<string>
    getPath(name: string): Promise<string>
  }

  // 系统信息
  system: {
    getPlatform(): Promise<string>
    getArch(): Promise<string>
    getNodeVersion(): Promise<string>
  }

  // 窗口控制
  window: {
    minimize(): Promise<void>
    maximize(): Promise<void>
    close(): Promise<void>
    isMaximized(): Promise<boolean>
    toggleFullscreen(): Promise<void>
  }

  // 对话框
  dialog: {
    showMessageBox(options: any): Promise<any>
    showErrorBox(title: string, content: string): Promise<void>
    showOpenDialog(options: any): Promise<any>
    showSaveDialog(options: any): Promise<any>
  }

  // 剪贴板
  clipboard: {
    writeText(text: string): Promise<void>
    readText(): Promise<string>
  }

  // Shell操作
  shell: {
    openExternal(url: string): Promise<void>
    showItemInFolder(fullPath: string): Promise<void>
    openPath(path: string): Promise<string>
  }

  // 通知
  notification: {
    show(options: any): Promise<boolean>
  }

  // 图书管理
  books: {
    findAll(): Promise<Book[]>
    findById(id: number): Promise<Book | null>
    search(query: string): Promise<Book[]>
    findByFormat(format: string): Promise<Book[]>
    findByAuthor(author: string): Promise<Book[]>
    create(bookData: CreateBookDto): Promise<Book>
    update(id: number, bookData: UpdateBookDto): Promise<Book>
    delete(id: number): Promise<void>
    batchDelete(ids: number[]): Promise<void>
    getStats(): Promise<BookStats>
    getRecentlyRead(limit?: number): Promise<Book[]>
    getFavorites(): Promise<Book[]>
    setFavorite(id: number, favorite: boolean): Promise<void>
    checkFileExists(filePath: string): Promise<boolean>
  }

  // 文件操作
  file: {
    import(filePaths: string[], options?: BookImportOptions): Promise<BookImportResult>
    openFileDialog(options?: any): Promise<string[]>
    openDirectoryDialog(options?: any): Promise<string[]>
    readFile(filePath: string): Promise<Buffer>
    writeFile(filePath: string, data: Buffer): Promise<void>
    exists(filePath: string): Promise<boolean>
    getFileStats(filePath: string): Promise<any>
  }

  // 设置管理
  settings: {
    get(key: string): Promise<any>
    set(key: string, value: any): Promise<void>
    getAll(): Promise<Record<string, any>>
    reset(): Promise<void>
  }

  // 学习功能
  learning: {
    translate(text: string, from: string, to: string): Promise<any>
    getWordDefinition(word: string): Promise<any>
    saveWord(word: string, translation: string, context: string): Promise<void>
    getVocabulary(): Promise<any[]>
    updateWordMastery(wordId: number, level: number): Promise<void>
  }

  // 数据库
  database: {
    healthCheck(): Promise<boolean>
  }

  // 日志记录
  log: {
    info(message: string, ...args: any[]): Promise<void>
    warn(message: string, ...args: any[]): Promise<void>
    error(message: string, ...args: any[]): Promise<void>
  }

  // 错误报告
  error: {
    report(error: any): Promise<void>
  }
}

// 创建API对象
const electronAPI: ElectronAPI = {
  // 应用信息
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    getName: () => ipcRenderer.invoke('app:getName'),
    getPath: (name: string) => ipcRenderer.invoke('app:getPath', name)
  },

  // 系统信息
  system: {
    getPlatform: () => ipcRenderer.invoke('system:getPlatform'),
    getArch: () => ipcRenderer.invoke('system:getArch'),
    getNodeVersion: () => ipcRenderer.invoke('system:getNodeVersion')
  },

  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized'),
    toggleFullscreen: () => ipcRenderer.invoke('window:toggleFullscreen')
  },

  // 对话框
  dialog: {
    showMessageBox: (options: any) => ipcRenderer.invoke('dialog:showMessageBox', options),
    showErrorBox: (title: string, content: string) => ipcRenderer.invoke('dialog:showErrorBox', title, content),
    showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:showOpenDialog', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:showSaveDialog', options)
  },

  // 剪贴板
  clipboard: {
    writeText: (text: string) => ipcRenderer.invoke('clipboard:writeText', text),
    readText: () => ipcRenderer.invoke('clipboard:readText')
  },

  // Shell操作
  shell: {
    openExternal: (url: string) => ipcRenderer.invoke('shell:openExternal', url),
    showItemInFolder: (fullPath: string) => ipcRenderer.invoke('shell:showItemInFolder', fullPath),
    openPath: (path: string) => ipcRenderer.invoke('shell:openPath', path)
  },

  // 通知
  notification: {
    show: (options: any) => ipcRenderer.invoke('notification:show', options)
  },

  // 图书管理
  books: {
    findAll: () => ipcRenderer.invoke('books:findAll'),
    findById: (id: number) => ipcRenderer.invoke('books:findById', id),
    search: (query: string) => ipcRenderer.invoke('books:search', query),
    findByFormat: (format: string) => ipcRenderer.invoke('books:findByFormat', format),
    findByAuthor: (author: string) => ipcRenderer.invoke('books:findByAuthor', author),
    create: (bookData: CreateBookDto) => ipcRenderer.invoke('books:create', bookData),
    update: (id: number, bookData: UpdateBookDto) => ipcRenderer.invoke('books:update', id, bookData),
    delete: (id: number) => ipcRenderer.invoke('books:delete', id),
    batchDelete: (ids: number[]) => ipcRenderer.invoke('books:batchDelete', ids),
    getStats: () => ipcRenderer.invoke('books:getStats'),
    getRecentlyRead: (limit?: number) => ipcRenderer.invoke('books:getRecentlyRead', limit),
    getFavorites: () => ipcRenderer.invoke('books:getFavorites'),
    setFavorite: (id: number, favorite: boolean) => ipcRenderer.invoke('books:setFavorite', id, favorite),
    checkFileExists: (filePath: string) => ipcRenderer.invoke('books:checkFileExists', filePath)
  },

  // 文件操作
  file: {
    import: (filePaths: string[], options?: BookImportOptions) => ipcRenderer.invoke('file:import', filePaths, options),
    openFileDialog: (options?: any) => ipcRenderer.invoke('file:openFileDialog', options),
    openDirectoryDialog: (options?: any) => ipcRenderer.invoke('file:openDirectoryDialog', options),
    readFile: (filePath: string) => ipcRenderer.invoke('file:readFile', filePath),
    writeFile: (filePath: string, data: Buffer) => ipcRenderer.invoke('file:writeFile', filePath, data),
    exists: (filePath: string) => ipcRenderer.invoke('file:exists', filePath),
    getFileStats: (filePath: string) => ipcRenderer.invoke('file:getFileStats', filePath)
  },

  // 设置管理
  settings: {
    get: (key: string) => ipcRenderer.invoke('settings:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('settings:set', key, value),
    getAll: () => ipcRenderer.invoke('settings:getAll'),
    reset: () => ipcRenderer.invoke('settings:reset')
  },

  // 学习功能
  learning: {
    translate: (text: string, from: string, to: string) => ipcRenderer.invoke('learning:translate', text, from, to),
    getWordDefinition: (word: string) => ipcRenderer.invoke('learning:getWordDefinition', word),
    saveWord: (word: string, translation: string, context: string) => ipcRenderer.invoke('learning:saveWord', word, translation, context),
    getVocabulary: () => ipcRenderer.invoke('learning:getVocabulary'),
    updateWordMastery: (wordId: number, level: number) => ipcRenderer.invoke('learning:updateWordMastery', wordId, level)
  },

  // 数据库
  database: {
    healthCheck: () => ipcRenderer.invoke('database:healthCheck')
  },

  // 日志记录
  log: {
    info: (message: string, ...args: any[]) => ipcRenderer.invoke('log:info', message, ...args),
    warn: (message: string, ...args: any[]) => ipcRenderer.invoke('log:warn', message, ...args),
    error: (message: string, ...args: any[]) => ipcRenderer.invoke('log:error', message, ...args)
  },

  // 错误报告
  error: {
    report: (error: any) => ipcRenderer.invoke('error:report', error)
  }
}

// 在渲染进程中暴露API
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// 类型声明，供TypeScript使用
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export type { ElectronAPI }
