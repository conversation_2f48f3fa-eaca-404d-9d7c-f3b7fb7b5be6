# 📖 用户故事管理目录

## 目录用途

本目录按照敏捷开发流程组织用户故事，支持故事的状态跟踪和生命周期管理。

## 目录结构

### 📋 backlog/
存储待开发的用户故事，按优先级排序。

**状态说明：**
- 故事已定义但尚未开始开发
- 包含完整的验收标准和估算
- 等待分配到Sprint中

**文件命名：**
- `story-{优先级}-{编号}-{简短描述}.md`
- 示例：`story-high-001-user-login.md`

### 🔄 in-progress/
存储正在开发中的用户故事。

**状态说明：**
- 故事已分配给开发团队
- 正在进行开发、测试或审查
- 包含开发进度和阻塞问题

**文件命名：**
- `story-{Sprint编号}-{编号}-{简短描述}.md`
- 示例：`story-sprint01-001-user-login.md`

### ✅ completed/
存储已完成的用户故事。

**状态说明：**
- 故事已通过所有验收标准
- 代码已合并到主分支
- 功能已部署到生产环境

**文件命名：**
- `story-{完成日期}-{编号}-{简短描述}.md`
- 示例：`story-20250801-001-user-login.md`

## 故事生命周期

### 1. 故事创建
```bash
@sm *draft
```

### 2. 故事质量检查
```bash
@sm *story-checklist
```

### 3. 故事开发
```bash
@dev *develop-story
```

### 4. 质量审查
```bash
@qa *review
```

### 5. 故事完成
移动到completed目录，更新状态

## 故事模板

### 标准用户故事格式
```markdown
# 用户故事：{标题}

## 故事描述
**作为** {用户角色}  
**我希望** {功能描述}  
**以便** {业务价值}

## 验收标准
1. [ ] 标准1：具体可测试的条件
2. [ ] 标准2：具体可测试的条件
3. [ ] 标准3：具体可测试的条件

## 技术要求
- 前端技术：{技术栈}
- 后端技术：{技术栈}
- 数据库：{数据模型}

## 工作量估算
- **故事点数：** {点数}
- **预估工时：** {小时}
- **复杂度：** {简单/中等/复杂}

## 依赖关系
- **前置条件：** {依赖的其他故事}
- **阻塞因素：** {可能的阻塞}

## 测试策略
- **单元测试：** {测试范围}
- **集成测试：** {测试场景}
- **用户测试：** {测试计划}

## 定义完成 (DoD)
- [ ] 代码开发完成
- [ ] 单元测试通过
- [ ] 代码审查通过
- [ ] 集成测试通过
- [ ] 用户验收通过
- [ ] 文档更新完成
```

## 状态管理

### 状态转换规则
1. **Backlog → In-Progress**：故事被分配到Sprint
2. **In-Progress → Completed**：故事通过所有验收标准
3. **任何状态 → Backlog**：故事需要重新规划

### 状态更新流程
1. 移动文件到对应目录
2. 更新文件内的状态字段
3. 记录状态变更日志
4. 通知相关团队成员

## 质量标准

### 故事质量检查清单
- [ ] 符合INVEST原则（独立、可协商、有价值、可估算、小、可测试）
- [ ] 验收标准明确具体
- [ ] 工作量估算合理
- [ ] 技术依赖清晰
- [ ] 测试策略完整

### 审查流程
1. **Scrum Master审查**：故事格式和质量
2. **产品负责人审查**：业务价值和优先级
3. **技术负责人审查**：技术可行性和架构影响
4. **团队评估**：工作量和实施计划

## 相关命令

### 故事管理命令
- `@sm *draft` - 创建新用户故事
- `@sm *story-checklist` - 执行故事质量检查
- `@sm *correct-course` - 项目纠偏和故事调整

### 开发相关命令
- `@dev *develop-story` - 开始故事开发
- `@dev *run-tests` - 执行测试
- `@qa *review` - 质量审查

### 产品管理命令
- `@po *create-story` - 从需求创建故事
- `@po *execute-checklist-po` - PO检查清单

## 报告和度量

### 常用度量指标
- **速度 (Velocity)**：每Sprint完成的故事点数
- **燃尽图 (Burndown)**：Sprint进度跟踪
- **周期时间 (Cycle Time)**：故事从开始到完成的时间
- **缺陷率**：故事返工的比例

### 报告生成
使用BMad分析工具生成项目报告：
```bash
@analyst *brainstorm "Sprint回顾和改进建议"
```

---

**维护者：** Scrum Master (SM)  
**创建时间：** 2025年8月1日  
**更新频率：** 每Sprint更新
