# 🔧 系统架构文档目录

## 目录用途

本目录存储系统架构设计和技术规范文档，为项目的技术实施提供详细的指导和参考。

## 目录结构

### 📊 diagrams/
存储各类架构图和设计图。

**图表类型：**
- **系统架构图**：整体系统结构和组件关系
- **部署架构图**：系统部署和基础设施
- **数据流图**：数据在系统中的流转
- **时序图**：系统交互的时间序列
- **组件图**：系统组件和接口关系

**文件格式：**
- `.mermaid` - Mermaid图表源码
- `.puml` - PlantUML图表源码
- `.png/.svg` - 导出的图片文件
- `.md` - 图表说明文档

**命名规范：**
- `{类型}-{名称}-v{版本}.{扩展名}`
- 示例：`system-architecture-v1.0.mermaid`

### 📋 specifications/
存储详细的技术规范文档。

**规范类型：**
- **API规范**：接口定义和协议
- **数据库规范**：数据模型和约束
- **安全规范**：安全策略和实施
- **性能规范**：性能要求和指标
- **集成规范**：第三方集成标准

**文件命名：**
- `{规范类型}-specification-v{版本}.md`
- 示例：`api-specification-v1.0.md`

### 📝 reviews/
存储架构审查记录和评估报告。

**审查类型：**
- **设计审查**：架构设计评审记录
- **代码审查**：关键代码的架构审查
- **性能审查**：性能测试和优化建议
- **安全审查**：安全评估和改进建议

**文件命名：**
- `{审查类型}-review-{日期}.md`
- 示例：`design-review-20250801.md`

## 架构文档创建流程

### 1. 架构设计
```bash
@architect *create-full-stack-architecture
```

### 2. 前端架构设计
```bash
@architect *create-front-end-architecture
```

### 3. 后端架构设计
```bash
@architect *create-backend-architecture
```

### 4. 架构验证
```bash
@architect *execute-checklist
```

### 5. 技术研究
```bash
@architect *research "{技术主题}"
```

## 架构文档模板

### 系统架构文档结构
```markdown
# 系统架构文档

## 1. 架构概述
- 系统目标和约束
- 架构原则和决策
- 关键质量属性

## 2. 架构视图
- 逻辑视图
- 开发视图
- 进程视图
- 物理视图

## 3. 技术栈
- 前端技术
- 后端技术
- 数据库技术
- 基础设施

## 4. 系统组件
- 核心组件
- 支撑组件
- 外部依赖

## 5. 数据架构
- 数据模型
- 数据流
- 存储策略

## 6. 安全架构
- 认证授权
- 数据保护
- 网络安全

## 7. 性能架构
- 性能目标
- 优化策略
- 监控方案

## 8. 部署架构
- 环境配置
- 部署策略
- 运维方案
```

### API规范文档结构
```markdown
# API规范文档

## 1. API概述
- API目标和范围
- 版本策略
- 认证方式

## 2. 接口设计原则
- RESTful设计
- 命名规范
- 错误处理

## 3. 接口列表
- 用户管理接口
- 业务功能接口
- 系统管理接口

## 4. 数据模型
- 请求模型
- 响应模型
- 错误模型

## 5. 安全规范
- 认证机制
- 授权策略
- 数据加密

## 6. 性能规范
- 响应时间要求
- 并发处理能力
- 限流策略
```

## 质量标准

### 架构文档质量检查
- [ ] 架构目标明确
- [ ] 技术选型有理由
- [ ] 组件职责清晰
- [ ] 接口定义完整
- [ ] 非功能需求覆盖
- [ ] 风险识别充分

### 图表质量标准
- [ ] 图表清晰易懂
- [ ] 标注信息完整
- [ ] 版本控制规范
- [ ] 格式统一标准

### 规范文档标准
- [ ] 规范具体可执行
- [ ] 示例代码正确
- [ ] 错误处理完整
- [ ] 版本兼容性说明

## 架构审查流程

### 设计审查
1. **架构师自审**：完整性和一致性检查
2. **技术团队评审**：可行性和实施难度评估
3. **产品团队确认**：业务需求匹配度验证
4. **外部专家咨询**：行业最佳实践对比

### 审查检查清单
- [ ] 架构满足功能需求
- [ ] 非功能需求得到保证
- [ ] 技术风险可控
- [ ] 实施计划合理
- [ ] 运维方案可行
- [ ] 扩展性考虑充分

## 架构演进管理

### 版本控制策略
- **主版本**：架构重大变更
- **次版本**：组件或接口变更
- **修订版本**：文档更新和错误修正

### 变更管理流程
1. 变更需求识别
2. 影响分析评估
3. 变更方案设计
4. 团队评审确认
5. 实施计划制定
6. 变更执行监控

### 架构债务管理
- 定期识别架构债务
- 评估债务影响和成本
- 制定偿还计划
- 跟踪偿还进度

## 工具和技术

### 图表工具
- **Mermaid**：代码化图表，易于版本控制
- **PlantUML**：UML图表生成
- **Draw.io**：在线图表编辑
- **Lucidchart**：专业架构图工具

### 文档工具
- **Markdown**：轻量级文档格式
- **GitBook**：文档网站生成
- **Confluence**：企业级文档平台
- **Notion**：协作文档平台

### 代码生成工具
- **OpenAPI Generator**：API代码生成
- **Swagger**：API文档生成
- **GraphQL**：API查询语言

## 相关命令参考

### 架构设计命令
```bash
@architect *create-full-stack-architecture    # 全栈架构设计
@architect *create-front-end-architecture     # 前端架构设计
@architect *create-backend-architecture       # 后端架构设计
@architect *create-brownfield-architecture    # 遗留系统架构
```

### 质量保证命令
```bash
@architect *execute-checklist                 # 架构检查清单
@qa *review                                   # 质量审查
@dev *run-tests                              # 架构验证测试
```

### 研究和分析命令
```bash
@architect *research "{技术主题}"              # 技术研究
@analyst *create-competitor-analysis          # 竞品技术分析
@analyst *brainstorm "{架构问题}"              # 架构问题分析
```

---

**维护者：** 架构师 (Architect)  
**创建时间：** 2025年8月1日  
**更新频率：** 根据架构演进需要更新
