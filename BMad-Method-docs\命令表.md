# BMad-Method 命令表与使用样例

## 📋 文档信息

**生成时间：** 2025年8月1日  
**BMad版本：** v4.33.1  
**适用环境：** Augment Code集成环境  
**文档类型：** 命令参考与实战样例  

## 🚀 系统级命令

### 环境管理命令
```bash
# 验证安装和配置
python .augment/validate_config.py

# 查看系统状态
python .augment/bmad_augment.py status

# 列出所有可用代理
python .augment/bmad_augment.py list

# 进入交互模式
python .augment/bmad_augment.py interactive

# 直接调用代理
python .augment/bmad_augment.py agent "@bmad-master"

# 生成代理激活提示词
python .augment/call_agent.py bmad-master "请帮我创建项目文档"
```

## 🤖 代理调用方式

### 在Augment Code中的调用方式
```bash
# 方式1：直接激活代理
@bmad-master

# 方式2：激活代理并执行命令
@dev *develop-story

# 方式3：带参数的命令
@pm *research "移动支付市场分析"
```

### 命令行调用方式
```bash
# 直接调用
python .augment/call_agent.py dev

# 带用户请求调用
python .augment/call_agent.py pm "需要创建用户登录功能的PRD"

# 交互模式调用
python .augment/bmad_augment.py interactive
# 然后输入：@bmad-master
```

## 📖 通用命令（所有代理支持）

### `*help` - 显示帮助信息
- **用法：** `*help`
- **描述：** 显示代理特定的帮助信息和命令列表
- **样例：** `@dev *help`

### `*exit` - 退出代理模式
- **用法：** `*exit`
- **描述：** 退出当前代理模式，返回普通对话
- **样例：** `@pm *exit`

## 🧙 BMad Master 命令详解

**调用方式：** `@bmad-master`

### `*help` - 显示所有可用命令
- **用法：** `*help`
- **描述：** 显示所有可用命令的编号列表，方便快速选择
- **样例：** `@bmad-master *help`

### `*kb` - 切换知识库模式
- **用法：** `*kb`
- **描述：** 切换知识库模式（默认关闭），开启后将加载并引用BMad知识库
- **样例：** `@bmad-master *kb`

### `*task` - 执行指定任务
- **用法：** `*task {任务名称}`
- **描述：** 执行指定任务，如果未指定任务名称，将显示所有可用任务列表
- **样例：** `@bmad-master *task create-doc`

### `*create-doc` - 使用模板创建文档
- **用法：** `*create-doc {模板名称}`
- **描述：** 使用指定模板创建文档，如果未指定模板，将显示所有可用模板列表
- **样例：** `@bmad-master *create-doc prd-tmpl`

### `*doc-out` - 输出完整文档
- **用法：** `*doc-out`
- **描述：** 将完整文档输出到当前目标文件
- **样例：** `@bmad-master *doc-out`

### `*document-project` - 项目文档化
- **用法：** `*document-project`
- **描述：** 执行项目文档化任务，分析项目结构并生成相关文档
- **样例：** `@bmad-master *document-project`

### `*execute-checklist` - 运行检查清单
- **用法：** `*execute-checklist {清单名称}`
- **描述：** 运行指定检查清单，如果未指定清单，将显示所有可用检查清单
- **样例：** `@bmad-master *execute-checklist pm-checklist`

### `*shard-doc` - 文档分片
- **用法：** `*shard-doc {文档} {目标位置}`
- **描述：** 将大文档分片到指定目标位置，便于管理和维护
- **样例：** `@bmad-master *shard-doc prd.md docs/prd/`

### `*yolo` - 切换Yolo模式
- **用法：** `*yolo`
- **描述：** 切换Yolo模式，启用快速执行模式
- **样例：** `@bmad-master *yolo`

### BMad Master 实战样例

#### 样例1：项目初始化文档化
```bash
# 激活BMad Master
@bmad-master

# 执行项目文档化
*document-project

# 预期输出：
# 正在分析项目结构...
# 生成项目概览文档...
# 创建技术债务报告...
# 文档已保存到 docs/project-overview.md
```

#### 样例2：创建PRD文档
```bash
@bmad-master
*create-doc prd-tmpl

# 系统会引导您填写：
# 1. 项目名称
# 2. 目标用户
# 3. 核心功能
# 4. 成功指标
```

## 💻 开发者 (Dev) 命令详解

**调用方式：** `@dev`

### `*help` - 显示开发者命令
- **用法：** `*help`
- **描述：** 显示所有可用的开发者专用命令列表
- **样例：** `@dev *help`

### `*develop-story` - 开发用户故事
- **用法：** `*develop-story`
- **描述：** 开发用户故事，执行完整的开发流程：读取任务→实现功能→编写测试→执行验证→更新状态
- **样例：** `@dev *develop-story`

### `*run-tests` - 执行代码检查和测试
- **用法：** `*run-tests`
- **描述：** 运行代码检查（linting）和自动化测试，确保代码质量
- **样例：** `@dev *run-tests`

### `*explain` - 详细解释操作
- **用法：** `*explain`
- **描述：** 详细解释刚才的操作过程，用于学习和培训，以培训初级工程师的方式说明
- **样例：** `@dev *explain`

### 开发者实战样例

#### 样例1：开发用户登录功能
```bash
# 激活开发者代理
@dev

# 开始开发故事
*develop-story

# 执行流程：
# 1. 读取story-001.md中的任务
# 2. 实现用户登录API
# 3. 编写单元测试
# 4. 执行测试验证
# 5. 更新任务状态为完成
# 6. 更新文件列表
```

#### 样例2：代码审查和重构
```bash
@dev
*run-tests

# 输出示例：
# 运行ESLint检查...
# 运行Jest单元测试...
# 测试覆盖率: 85%
# 发现3个代码质量问题
```

## 📋 产品经理 (PM) 命令详解

**调用方式：** `@pm`

### `*help` - 显示PM命令
- **用法：** `*help`
- **描述：** 显示所有可用的产品经理专用命令列表
- **样例：** `@pm *help`

### `*create` - 创建产品需求文档
- **用法：** `*create`
- **描述：** 创建新的产品需求文档（PRD），使用create-doc任务和prd-tmpl模板
- **样例：** `@pm *create`

### `*create-brownfield-prd` - 创建棕地项目PRD
- **用法：** `*create-brownfield-prd`
- **描述：** 为现有项目（棕地项目）创建PRD，使用brownfield-prd-tmpl模板
- **样例：** `@pm *create-brownfield-prd`

### `*research` - 进行深度研究
- **用法：** `*research {主题}`
- **描述：** 对指定主题进行深度研究，执行create-deep-research-prompt任务
- **样例：** `@pm *research "电商支付系统"`

### `*create-epic` - 创建史诗
- **用法：** `*create-epic`
- **描述：** 为棕地项目创建史诗，执行brownfield-create-epic任务
- **样例：** `@pm *create-epic`

### `*create-story` - 创建用户故事
- **用法：** `*create-story`
- **描述：** 从需求创建用户故事，执行brownfield-create-story任务
- **样例：** `@pm *create-story`

### `*doc-out` - 输出文档
- **用法：** `*doc-out`
- **描述：** 将完整的PRD文档输出到当前目标文件
- **样例：** `@pm *doc-out`

### `*shard-prd` - PRD分片
- **用法：** `*shard-prd`
- **描述：** 对提供的prd.md运行分片任务，如果未找到prd.md会询问路径
- **样例：** `@pm *shard-prd`

### 产品经理实战样例

#### 样例1：创建电商应用PRD
```bash
@pm
*create

# 交互式创建过程：
# 项目名称: 智能电商平台
# 目标用户: 18-35岁都市消费者
# 核心功能: 商品浏览、购物车、支付、订单管理
# 商业目标: 提升用户购买转化率30%
```

#### 样例2：市场研究
```bash
@pm
*research "移动支付市场趋势分析"

# 输出：
# 正在生成深度研究提示词...
# 研究范围：移动支付技术发展、用户行为、竞争格局
# 建议研究方法：用户调研、竞品分析、数据分析
```

## 🏗️ 架构师 (Architect) 命令详解

**调用方式：** `@architect`

### `*help` - 显示架构师命令
- **用法：** `*help`
- **描述：** 显示所有可用的架构师专用命令列表
- **样例：** `@architect *help`

### `*create-full-stack-architecture` - 创建全栈架构文档
- **用法：** `*create-full-stack-architecture`
- **描述：** 创建全栈架构文档，使用fullstack-architecture-tmpl.yaml模板
- **样例：** `@architect *create-full-stack-architecture`

### `*create-backend-architecture` - 创建后端架构文档
- **用法：** `*create-backend-architecture`
- **描述：** 创建后端架构文档，使用architecture-tmpl.yaml模板
- **样例：** `@architect *create-backend-architecture`

### `*create-front-end-architecture` - 创建前端架构文档
- **用法：** `*create-front-end-architecture`
- **描述：** 创建前端架构文档，使用front-end-architecture-tmpl.yaml模板
- **样例：** `@architect *create-front-end-architecture`

### `*create-brownfield-architecture` - 创建棕地项目架构文档
- **用法：** `*create-brownfield-architecture`
- **描述：** 创建棕地项目架构文档，使用brownfield-architecture-tmpl.yaml模板
- **样例：** `@architect *create-brownfield-architecture`

### `*research` - 进行技术研究
- **用法：** `*research {主题}`
- **描述：** 对指定技术主题进行深度研究，执行create-deep-research-prompt任务
- **样例：** `@architect *research "微服务架构"`

### `*execute-checklist` - 执行架构检查清单
- **用法：** `*execute-checklist {清单名称}`
- **描述：** 运行架构相关的检查清单，默认运行architect-checklist
- **样例：** `@architect *execute-checklist`

### 架构师实战样例

#### 样例1：设计微服务架构
```bash
@architect
*create-full-stack-architecture

# 引导设计过程：
# 1. 技术栈选择：Node.js + React + MongoDB
# 2. 架构模式：微服务 + API网关
# 3. 数据库设计：分库分表策略
# 4. 缓存策略：Redis集群
# 5. 部署方案：Docker + Kubernetes
```

#### 样例2：技术选型研究
```bash
@architect
*research "容器化部署最佳实践"

# 输出：
# 正在分析容器化技术栈...
# 推荐方案：Docker + Kubernetes + Helm
# 关键考虑因素：性能、安全性、可维护性
```

## 🔍 QA工程师 (QA) 命令详解

**调用方式：** `@qa`

### `*help` - 显示QA命令
- **用法：** `*help`
- **描述：** 显示所有可用的QA工程师专用命令列表
- **样例：** `@qa *help`

### `*review` - 审查用户故事
- **用法：** `*review {故事名称}`
- **描述：** 审查指定的用户故事，执行review-story任务。如果未指定故事，会审查docs/stories中的最高序号故事
- **样例：** `@qa *review story-001`
- **注意：** QA代理在审查故事时只能更新"QA Results"部分，不能修改其他部分

### QA工程师实战样例

#### 样例1：代码审查
```bash
@qa
*review

# 审查过程：
# 1. 检查代码质量和规范
# 2. 验证测试覆盖率
# 3. 检查安全漏洞
# 4. 更新QA Results部分
```

## 📈 业务分析师 (Analyst) 命令详解

**调用方式：** `@analyst`

### `*help` - 显示分析师命令
- **用法：** `*help`
- **描述：** 显示所有可用的业务分析师专用命令列表
- **样例：** `@analyst *help`

### `*create-project-brief` - 创建项目简介
- **用法：** `*create-project-brief`
- **描述：** 创建项目简介文档，使用project-brief-tmpl.yaml模板
- **样例：** `@analyst *create-project-brief`

### `*perform-market-research` - 执行市场研究
- **用法：** `*perform-market-research`
- **描述：** 执行市场研究分析，使用market-research-tmpl.yaml模板
- **样例：** `@analyst *perform-market-research`

### `*create-competitor-analysis` - 创建竞品分析
- **用法：** `*create-competitor-analysis`
- **描述：** 创建竞品分析报告，使用competitor-analysis-tmpl.yaml模板
- **样例：** `@analyst *create-competitor-analysis`

### `*brainstorm` - 促进头脑风暴会议
- **用法：** `*brainstorm {主题}`
- **描述：** 促进结构化头脑风暴会议，运行facilitate-brainstorming-session.md任务
- **样例：** `@analyst *brainstorm "产品创新点"`

### `*elicit` - 运行高级启发任务
- **用法：** `*elicit`
- **描述：** 运行高级需求启发任务，执行advanced-elicitation任务
- **样例：** `@analyst *elicit`

### 业务分析师实战样例

#### 样例1：竞品分析
```bash
@analyst
*create-competitor-analysis

# 分析维度：
# 1. 竞品功能对比
# 2. 用户体验评估
# 3. 商业模式分析
# 4. 技术架构对比
# 5. 市场定位分析
```

## 🎨 UX专家 (UX Expert) 命令详解

**调用方式：** `@ux-expert`

### `*help` - 显示UX命令
- **用法：** `*help`
- **描述：** 显示所有可用的UX专家专用命令列表
- **样例：** `@ux-expert *help`

### `*create-front-end-spec` - 创建前端规范文档
- **用法：** `*create-front-end-spec`
- **描述：** 创建前端规范文档，使用front-end-spec-tmpl.yaml模板
- **样例：** `@ux-expert *create-front-end-spec`

### `*generate-ui-prompt` - 生成AI UI提示词
- **用法：** `*generate-ui-prompt`
- **描述：** 生成AI UI设计提示词，运行generate-ai-frontend-prompt.md任务
- **样例：** `@ux-expert *generate-ui-prompt`

## 📊 产品负责人 (PO) 命令详解

**调用方式：** `@po`

### `*help` - 显示PO命令
- **用法：** `*help`
- **描述：** 显示所有可用的产品负责人专用命令列表
- **样例：** `@po *help`

### `*execute-checklist-po` - 运行PO检查清单
- **用法：** `*execute-checklist-po`
- **描述：** 运行产品负责人专用检查清单，执行po-master-checklist检查清单
- **样例：** `@po *execute-checklist-po`

### `*shard-doc` - 文档分片
- **用法：** `*shard-doc {文档} {目标}`
- **描述：** 将指定文档分片到目标位置，运行shard-doc任务
- **样例：** `@po *shard-doc requirements.md docs/requirements/`

### `*correct-course` - 执行纠偏任务
- **用法：** `*correct-course`
- **描述：** 执行项目纠偏任务，运行correct-course任务
- **样例：** `@po *correct-course`

### `*create-epic` - 创建史诗
- **用法：** `*create-epic`
- **描述：** 为棕地项目创建史诗，执行brownfield-create-epic任务
- **样例：** `@po *create-epic`

### `*create-story` - 创建用户故事
- **用法：** `*create-story`
- **描述：** 从需求创建用户故事，执行brownfield-create-story任务
- **样例：** `@po *create-story`

### `*doc-out` - 输出文档
- **用法：** `*doc-out`
- **描述：** 将完整文档输出到当前目标文件
- **样例：** `@po *doc-out`

## 🏃 Scrum Master (SM) 命令详解

**调用方式：** `@sm`

### `*help` - 显示SM命令
- **用法：** `*help`
- **描述：** 显示所有可用的Scrum Master专用命令列表
- **样例：** `@sm *help`

### `*draft` - 创建下一个故事
- **用法：** `*draft`
- **描述：** 执行创建下一个故事任务，运行create-next-story.md任务
- **样例：** `@sm *draft`

### `*correct-course` - 执行纠偏任务
- **用法：** `*correct-course`
- **描述：** 执行项目纠偏任务，运行correct-course.md任务
- **样例：** `@sm *correct-course`

### `*story-checklist` - 执行故事检查清单
- **用法：** `*story-checklist`
- **描述：** 执行故事检查清单，运行story-draft-checklist.md检查清单
- **样例：** `@sm *story-checklist`

### UX专家实战样例

#### 样例1：设计移动端界面
```bash
@ux-expert
*generate-ui-prompt

# 生成AI UI提示词：
# "创建一个现代化的移动端登录界面，包含邮箱输入、密码输入、
# 记住我选项和登录按钮，使用Material Design风格"
```

## 🔄 完整工作流样例

### 工作流样例1：新项目开发完整流程

**场景描述：** 从零开始开发一个电商平台项目
**目标：** 建立完整的项目基础，从需求分析到首个功能模块上线
**预估时间：** 4-6周
**参与角色：** 全部代理

#### 阶段1：项目启动和需求分析 (第1周)

```bash
# 步骤1：项目初始化和现状分析 (30分钟)
@bmad-master
*document-project

# 预期输出：项目概览文档、技术债务报告、改进建议
# 关键文件：docs/project-overview.md

# 步骤2：市场研究和竞品分析 (2小时)
@analyst
*create-project-brief
*perform-market-research
*create-competitor-analysis

# 预期输出：项目简介、市场研究报告、竞品分析报告
# 关键文件：docs/project-brief.md, docs/market-research.md

# 步骤3：深度需求研究 (1小时)
@pm
*research "电商平台用户行为分析"

# 预期输出：深度研究提示词和分析框架
# 用途：指导后续用户调研和需求分析

# 步骤4：产品需求文档创建 (3小时)
@pm
*create

# 预期输出：完整的PRD文档
# 关键文件：docs/prd.md
# 包含：目标用户、功能需求、非功能需求、Epic规划

# 步骤5：PRD文档分片管理 (30分钟)
@pm
*shard-prd

# 预期输出：分片后的PRD文档结构
# 关键目录：docs/prd/（包含各个模块的详细文档）
```

#### 阶段2：架构设计和技术选型 (第2周)

```bash
# 步骤6：全栈架构设计 (4小时)
@architect
*create-full-stack-architecture

# 预期输出：完整的系统架构文档
# 关键文件：docs/architecture.md
# 包含：技术栈选择、系统架构图、数据库设计

# 步骤7：技术可行性研究 (2小时)
@architect
*research "微服务架构在电商平台中的应用"

# 预期输出：技术研究报告和建议
# 用途：验证架构设计的可行性

# 步骤8：架构设计验证 (1小时)
@architect
*execute-checklist

# 预期输出：架构检查清单结果
# 验证项：性能、安全性、可扩展性、可维护性

# 步骤9：前端规范设计 (3小时)
@ux-expert
*create-front-end-spec

# 预期输出：前端开发规范文档
# 关键文件：docs/frontend-spec.md
# 包含：UI组件库、设计系统、交互规范

# 步骤10：UI设计提示词生成 (30分钟)
@ux-expert
*generate-ui-prompt

# 预期输出：AI UI设计提示词
# 用途：指导UI设计师或AI工具生成界面设计
```

#### 阶段3：敏捷规划和故事创建 (第3周)

```bash
# 步骤11：Epic创建和规划 (2小时)
@po
*create-epic

# 预期输出：Epic列表和优先级规划
# 关键文件：docs/epics/（各个Epic的详细文档）

# 步骤12：用户故事创建 (4小时)
@sm
*draft

# 预期输出：详细的用户故事
# 关键文件：docs/stories/story-001.md
# 包含：用户故事描述、验收标准、估算

# 步骤13：故事质量检查 (1小时)
@sm
*story-checklist

# 预期输出：故事质量检查报告
# 验证项：INVEST原则、验收标准完整性

# 步骤14：产品负责人审核 (1小时)
@po
*execute-checklist-po

# 预期输出：PO检查清单结果
# 验证项：业务价值、优先级、依赖关系

# 步骤15：需求启发和细化 (2小时)
@analyst
*elicit

# 预期输出：细化的需求和边界条件
# 用途：补充遗漏的需求和异常场景
```

#### 阶段4：开发实施和质量保证 (第4-5周)

```bash
# 步骤16：开发环境准备和首个故事开发 (1周)
@dev
*develop-story

# 预期输出：可工作的软件功能
# 关键文件：实现代码、单元测试、集成测试
# 验证：功能完整性、代码质量

# 步骤17：代码测试和质量检查 (4小时)
@dev
*run-tests

# 预期输出：测试报告和代码质量报告
# 包含：单元测试覆盖率、集成测试结果、代码规范检查

# 步骤18：开发过程解释和知识传递 (1小时)
@dev
*explain

# 预期输出：详细的开发过程说明
# 用途：团队知识分享、新人培训

# 步骤19：质量审查和代码评审 (2小时)
@qa
*review

# 预期输出：QA审查报告
# 关键文件：docs/stories/story-001.md（更新QA Results部分）
# 验证：功能正确性、性能、安全性

# 步骤20：项目纠偏和改进 (1小时)
@po
*correct-course

# 预期输出：项目改进建议和行动计划
# 用途：识别偏差、调整方向
```

#### 阶段5：文档整理和发布准备 (第6周)

```bash
# 步骤21：完整文档输出 (1小时)
@bmad-master
*doc-out

# 预期输出：整合的项目文档
# 用途：项目交付、知识保存

# 步骤22：文档分片和组织 (30分钟)
@bmad-master
*shard-doc "docs/complete-project-doc.md" "docs/final/"

# 预期输出：结构化的文档目录
# 便于：文档维护、团队查阅

# 步骤23：项目总结和经验提取 (1小时)
@analyst
*brainstorm "项目成功因素和改进点"

# 预期输出：项目复盘报告
# 用途：经验积累、流程改进
```

#### 关键命令速查

**项目启动阶段：**
- `@bmad-master *document-project` - 项目分析
- `@analyst *create-project-brief` - 项目简介
- `@pm *create` - PRD创建

**设计阶段：**
- `@architect *create-full-stack-architecture` - 架构设计
- `@ux-expert *create-front-end-spec` - 前端规范

**开发阶段：**
- `@sm *draft` - 故事创建
- `@dev *develop-story` - 功能开发
- `@qa *review` - 质量审查

**常见问题处理：**
- 如果架构设计不通过检查：重新执行 `@architect *research` 进行深度研究
- 如果故事不符合标准：使用 `@sm *story-checklist` 重新检查
- 如果需求不清晰：执行 `@analyst *elicit` 进行需求启发

---

### 工作流样例2：现有项目优化改进流程

**场景描述：** 对现有的遗留系统进行现代化改造
**目标：** 识别技术债务，制定改进计划，实施关键优化
**预估时间：** 3-4周
**参与角色：** 架构师、产品经理、开发者、分析师

#### 阶段1：现状分析和问题识别 (第1周)

```bash
# 步骤1：项目现状全面分析 (2小时)
@bmad-master
*document-project

# 预期输出：
# - 项目概览文档（当前架构、技术栈、功能模块）
# - 技术债务报告（代码质量、性能问题、安全风险）
# - 改进建议报告（优先级排序的改进项）

# 步骤2：业务影响分析 (3小时)
@analyst
*perform-market-research
*create-competitor-analysis

# 预期输出：
# - 市场环境变化分析
# - 竞品功能对比
# - 业务机会识别

# 步骤3：技术架构深度分析 (4小时)
@architect
*create-brownfield-architecture

# 预期输出：
# - 现有架构问题诊断
# - 目标架构设计
# - 迁移路径规划
# 关键文件：docs/brownfield-architecture.md

# 步骤4：改进需求定义 (3小时)
@pm
*create-brownfield-prd

# 预期输出：
# - 改进需求文档
# - 业务价值评估
# - 风险评估
# 关键文件：docs/improvement-prd.md
```

#### 阶段2：改进计划制定 (第2周)

```bash
# 步骤5：Epic级别改进规划 (2小时)
@po
*create-epic

# 预期输出：
# - 改进Epic列表（如：性能优化、安全加固、UI现代化）
# - Epic优先级和依赖关系
# - 业务价值评估

# 步骤6：详细改进故事创建 (4小时)
@po
*create-story

# 预期输出：
# - 具体的改进用户故事
# - 技术任务分解
# - 验收标准定义
# 关键文件：docs/stories/improvement-story-001.md

# 步骤7：技术可行性验证 (3小时)
@architect
*research "遗留系统现代化最佳实践"

# 预期输出：
# - 技术方案可行性分析
# - 风险缓解策略
# - 实施建议

# 步骤8：改进计划审核 (1小时)
@po
*execute-checklist-po

# 预期输出：
# - 计划完整性检查
# - 风险评估结果
# - 资源需求确认
```

#### 阶段3：关键改进实施 (第3周)

```bash
# 步骤9：高优先级改进开发 (1周)
@dev
*develop-story

# 预期输出：
# - 重构后的代码模块
# - 性能优化实现
# - 新功能开发
# 验证：向后兼容性、性能提升

# 步骤10：改进效果测试 (4小时)
@dev
*run-tests

# 预期输出：
# - 回归测试结果
# - 性能基准测试
# - 安全测试报告

# 步骤11：质量保证审查 (3小时)
@qa
*review

# 预期输出：
# - 改进质量评估
# - 风险点识别
# - 上线建议
```

#### 阶段4：效果评估和持续改进 (第4周)

```bash
# 步骤12：改进效果分析 (2小时)
@analyst
*brainstorm "系统改进效果评估"

# 预期输出：
# - 改进前后对比分析
# - KPI提升情况
# - 用户反馈收集

# 步骤13：项目纠偏和优化 (1小时)
@sm
*correct-course

# 预期输出：
# - 改进计划调整建议
# - 下一阶段规划
# - 风险应对措施

# 步骤14：知识文档化 (2小时)
@bmad-master
*doc-out

# 预期输出：
# - 改进过程文档
# - 最佳实践总结
# - 经验教训记录
```

#### 关键命令速查

**现状分析：**
- `@bmad-master *document-project` - 全面项目分析
- `@architect *create-brownfield-architecture` - 遗留系统架构分析
- `@pm *create-brownfield-prd` - 改进需求定义

**改进规划：**
- `@po *create-epic` - Epic级改进规划
- `@po *create-story` - 详细故事创建
- `@architect *research` - 技术方案研究

**实施验证：**
- `@dev *develop-story` - 改进实施
- `@qa *review` - 质量审查
- `@sm *correct-course` - 计划调整

**故障排除：**
- 如果改进效果不明显：使用 `@analyst *brainstorm` 重新分析
- 如果技术方案有问题：执行 `@architect *execute-checklist` 验证
- 如果需求不清晰：使用 `@analyst *elicit` 深度挖掘

### 工作流样例3：技术架构设计专项流程

**场景描述：** 为复杂的企业级应用设计完整的技术架构
**目标：** 建立可扩展、高性能、安全的系统架构
**预估时间：** 2-3周
**参与角色：** 架构师、分析师、UX专家、产品经理

#### 阶段1：需求分析和架构调研 (第1周)

```bash
# 步骤1：业务需求深度分析 (3小时)
@analyst
*create-project-brief
*elicit

# 预期输出：
# - 详细的业务需求文档
# - 非功能性需求清单（性能、安全、可用性）
# - 约束条件和边界定义

# 步骤2：技术趋势研究 (4小时)
@architect
*research "云原生架构设计最佳实践"
*research "微服务架构模式选择"

# 预期输出：
# - 技术趋势分析报告
# - 架构模式对比分析
# - 技术选型建议

# 步骤3：竞品技术架构分析 (3小时)
@analyst
*create-competitor-analysis

# 预期输出：
# - 竞品技术架构对比
# - 性能基准分析
# - 技术优势识别

# 步骤4：用户体验需求分析 (2小时)
@ux-expert
*create-front-end-spec

# 预期输出：
# - 前端性能要求
# - 用户体验标准
# - 界面交互需求
```

#### 阶段2：架构设计和建模 (第2周)

```bash
# 步骤5：全栈架构设计 (1天)
@architect
*create-full-stack-architecture

# 预期输出：
# - 完整的系统架构图
# - 技术栈选择和理由
# - 部署架构设计
# 关键文件：docs/architecture.md

# 步骤6：前端架构专项设计 (4小时)
@architect
*create-front-end-architecture

# 预期输出：
# - 前端架构详细设计
# - 组件架构规划
# - 状态管理方案
# 关键文件：docs/frontend-architecture.md

# 步骤7：后端架构专项设计 (4小时)
@architect
*create-backend-architecture

# 预期输出：
# - 后端服务架构
# - 数据库设计
# - API设计规范
# 关键文件：docs/backend-architecture.md

# 步骤8：架构设计验证 (2小时)
@architect
*execute-checklist

# 预期输出：
# - 架构质量检查报告
# - 风险点识别
# - 改进建议
```

#### 阶段3：架构验证和优化 (第3周)

```bash
# 步骤9：架构原型验证 (3天)
@dev
*develop-story

# 预期输出：
# - 架构原型代码
# - 关键技术验证
# - 性能基准测试
# 验证：技术可行性、性能指标

# 步骤10：架构测试和评估 (1天)
@dev
*run-tests

# 预期输出：
# - 架构性能测试报告
# - 负载测试结果
# - 安全测试评估

# 步骤11：架构质量审查 (4小时)
@qa
*review

# 预期输出：
# - 架构质量评估报告
# - 安全性审查结果
# - 可维护性评估

# 步骤12：架构文档完善 (2小时)
@bmad-master
*doc-out

# 预期输出：
# - 完整的架构文档包
# - 技术决策记录
# - 实施指南
```

#### 关键命令速查

**需求分析：**
- `@analyst *create-project-brief` - 业务需求分析
- `@analyst *elicit` - 需求深度挖掘
- `@ux-expert *create-front-end-spec` - 前端需求

**架构设计：**
- `@architect *create-full-stack-architecture` - 全栈架构
- `@architect *create-front-end-architecture` - 前端架构
- `@architect *create-backend-architecture` - 后端架构

**验证优化：**
- `@architect *execute-checklist` - 架构检查
- `@dev *develop-story` - 原型验证
- `@qa *review` - 质量审查

**技术难点处理：**
- 架构复杂度过高：使用 `@architect *research` 寻找简化方案
- 性能要求不明确：执行 `@analyst *elicit` 细化需求
- 技术选型困难：使用 `@analyst *create-competitor-analysis` 对比分析

---

### 工作流样例4：产品需求分析专项流程

**场景描述：** 为新产品功能进行全面的需求分析和规划
**目标：** 产出高质量的PRD文档和可执行的开发计划
**预估时间：** 2周
**参与角色：** 产品经理、业务分析师、UX专家、产品负责人

#### 阶段1：市场和用户研究 (第1周前半)

```bash
# 步骤1：市场环境分析 (1天)
@analyst
*perform-market-research
*create-competitor-analysis

# 预期输出：
# - 市场趋势分析报告
# - 竞品功能对比矩阵
# - 市场机会识别
# 关键文件：docs/market-research.md

# 步骤2：用户需求深度挖掘 (4小时)
@analyst
*elicit
*brainstorm "用户痛点和需求场景"

# 预期输出：
# - 用户需求清单
# - 用户旅程地图
# - 痛点优先级排序

# 步骤3：产品定位研究 (3小时)
@pm
*research "目标用户行为分析"

# 预期输出：
# - 用户画像定义
# - 使用场景分析
# - 产品定位建议
```

#### 阶段2：需求定义和文档化 (第1周后半)

```bash
# 步骤4：PRD文档创建 (1天)
@pm
*create

# 预期输出：
# - 完整的PRD文档
# - 功能需求列表
# - 非功能需求定义
# 关键文件：docs/prd.md

# 步骤5：用户体验需求定义 (4小时)
@ux-expert
*create-front-end-spec
*generate-ui-prompt

# 预期输出：
# - 用户体验规范
# - 界面设计要求
# - 交互流程定义

# 步骤6：PRD文档结构化 (1小时)
@pm
*shard-prd

# 预期输出：
# - 模块化的PRD文档
# - 便于团队协作的文档结构
# 关键目录：docs/prd/
```

#### 阶段3：需求验证和规划 (第2周)

```bash
# 步骤7：Epic级别规划 (4小时)
@po
*create-epic

# 预期输出：
# - Epic列表和描述
# - 业务价值评估
# - 开发优先级排序
# 关键文件：docs/epics/

# 步骤8：用户故事创建 (1天)
@po
*create-story

# 预期输出：
# - 详细的用户故事
# - 验收标准定义
# - 工作量估算
# 关键文件：docs/stories/

# 步骤9：需求质量检查 (2小时)
@po
*execute-checklist-po

# 预期输出：
# - 需求完整性检查
# - 业务价值验证
# - 可行性评估

# 步骤10：需求最终确认 (2小时)
@analyst
*brainstorm "需求风险和缓解措施"

# 预期输出：
# - 需求风险评估
# - 缓解措施建议
# - 需求变更管理计划

# 步骤11：需求文档整合 (1小时)
@bmad-master
*doc-out

# 预期输出：
# - 完整的需求文档包
# - 需求追溯矩阵
# - 开发指导文档
```

#### 关键命令速查

**市场研究：**
- `@analyst *perform-market-research` - 市场分析
- `@analyst *create-competitor-analysis` - 竞品分析
- `@pm *research` - 专项研究

**需求定义：**
- `@pm *create` - PRD创建
- `@ux-expert *create-front-end-spec` - UX需求
- `@pm *shard-prd` - 文档结构化

**需求规划：**
- `@po *create-epic` - Epic规划
- `@po *create-story` - 故事创建
- `@po *execute-checklist-po` - 质量检查

**需求问题处理：**
- 需求不清晰：使用 `@analyst *elicit` 深度挖掘
- 用户价值不明确：执行 `@analyst *brainstorm` 重新分析
- 需求冲突：使用 `@po *correct-course` 进行纠偏

---

### 工作流样例5：质量保证专项流程

**场景描述：** 建立完整的质量保证体系和流程
**目标：** 确保产品质量，建立持续改进机制
**预估时间：** 1-2周
**参与角色：** QA工程师、开发者、架构师、Scrum Master

#### 阶段1：质量标准建立 (第1周前半)

```bash
# 步骤1：质量基线分析 (4小时)
@bmad-master
*document-project

# 预期输出：
# - 当前质量状况分析
# - 质量问题识别
# - 改进机会评估

# 步骤2：质量标准研究 (3小时)
@architect
*research "软件质量保证最佳实践"

# 预期输出：
# - 行业质量标准对比
# - 最佳实践总结
# - 质量工具推荐

# 步骤3：测试策略设计 (4小时)
@qa
*review

# 预期输出：
# - 测试策略文档
# - 测试类型定义
# - 质量门禁标准
# 关键文件：docs/testing-strategy.md
```

#### 阶段2：质量流程实施 (第1周后半)

```bash
# 步骤4：代码质量检查流程 (1天)
@dev
*run-tests

# 预期输出：
# - 自动化测试套件
# - 代码质量报告
# - 持续集成配置

# 步骤5：故事质量检查 (4小时)
@sm
*story-checklist

# 预期输出：
# - 故事质量标准
# - 检查清单模板
# - 质量评估流程

# 步骤6：开发过程质量保证 (3小时)
@dev
*explain

# 预期输出：
# - 开发规范文档
# - 代码审查标准
# - 质量培训材料
```

#### 阶段3：质量监控和改进 (第2周)

```bash
# 步骤7：质量数据收集 (持续进行)
@qa
*review

# 预期输出：
# - 质量指标仪表板
# - 缺陷趋势分析
# - 质量改进建议

# 步骤8：质量问题分析 (2小时)
@analyst
*brainstorm "质量问题根因分析"

# 预期输出：
# - 问题根因分析报告
# - 改进措施建议
# - 预防措施计划

# 步骤9：流程持续改进 (2小时)
@sm
*correct-course

# 预期输出：
# - 流程改进计划
# - 质量目标调整
# - 团队培训计划

# 步骤10：质量文档整理 (1小时)
@bmad-master
*doc-out

# 预期输出：
# - 完整的质量手册
# - 流程操作指南
# - 质量改进记录
```

#### 关键命令速查

**质量分析：**
- `@bmad-master *document-project` - 质量现状分析
- `@architect *research` - 最佳实践研究
- `@analyst *brainstorm` - 问题分析

**质量实施：**
- `@qa *review` - 质量审查
- `@dev *run-tests` - 自动化测试
- `@sm *story-checklist` - 故事质量检查

**质量改进：**
- `@sm *correct-course` - 流程改进
- `@dev *explain` - 知识传递
- `@bmad-master *doc-out` - 文档整理

**质量问题处理：**
- 测试覆盖率不足：使用 `@dev *run-tests` 增强测试
- 质量标准不明确：执行 `@architect *research` 研究标准
- 团队质量意识不足：使用 `@dev *explain` 进行培训

---

## 🎯 使用技巧与最佳实践

### 命令使用技巧

#### 基础使用原则
1. **查看帮助优先**：每个代理激活后先使用 `*help`
   ```bash
   @pm
   *help
   # 查看所有可用命令，了解代理能力
   ```

2. **分步执行**：复杂任务分解为多个命令
   ```bash
   # 错误方式：一次性完成所有工作
   # 正确方式：分步骤执行
   @pm *create
   @pm *shard-prd
   @architect *create-full-stack-architecture
   ```

3. **验证结果**：重要操作后检查输出
   ```bash
   @architect *create-backend-architecture
   @architect *execute-checklist  # 验证架构质量
   ```

4. **保持上下文**：在同一会话中使用相关代理
   ```bash
   # 保持项目上下文，避免重复说明
   @pm *create
   @architect *create-full-stack-architecture  # 基于PRD设计架构
   ```

#### 高级使用技巧

**1. 命令链式执行**
```bash
# 项目启动快速链
@bmad-master *document-project
@analyst *create-project-brief
@pm *create
@pm *shard-prd

# 开发质量链
@dev *develop-story
@dev *run-tests
@qa *review
```

**2. 并行工作流**
```bash
# 可以并行执行的任务
# 线程1：架构设计
@architect *create-full-stack-architecture

# 线程2：用户体验设计（同时进行）
@ux-expert *create-front-end-spec

# 线程3：市场分析（同时进行）
@analyst *perform-market-research
```

**3. 迭代优化模式**
```bash
# 第一轮：快速原型
@pm *create
@architect *create-backend-architecture
@dev *develop-story

# 第二轮：质量提升
@qa *review
@architect *execute-checklist
@sm *correct-course

# 第三轮：持续改进
@analyst *brainstorm "优化建议"
@dev *explain
```

### 代理协作模式

#### 标准协作流程

**需求驱动流程：**
```bash
@analyst → @pm → @architect → @ux-expert → @sm → @dev → @qa
```

**质量优先流程：**
```bash
@bmad-master → @architect → @qa → @dev → @sm → @po
```

**快速迭代流程：**
```bash
@pm → @dev → @qa → @sm → @po → @pm (循环)
```

#### 专项协作模式

**技术架构专项：**
- 主导：`@architect`
- 支持：`@analyst` (需求分析) + `@ux-expert` (前端需求)
- 验证：`@dev` (可行性) + `@qa` (质量标准)

**产品规划专项：**
- 主导：`@pm`
- 支持：`@analyst` (市场研究) + `@po` (业务价值)
- 验证：`@ux-expert` (用户体验) + `@sm` (可执行性)

**开发实施专项：**
- 主导：`@dev`
- 支持：`@sm` (任务管理) + `@architect` (技术指导)
- 验证：`@qa` (质量保证) + `@po` (业务验收)

### 常用命令组合

#### 项目启动组合
```bash
# 完整项目启动（30分钟）
@bmad-master *document-project
@analyst *create-project-brief
@pm *create
@pm *shard-prd
@architect *create-full-stack-architecture

# 快速启动（10分钟）
@bmad-master *document-project
@pm *create
@architect *create-backend-architecture
```

#### 开发质量组合
```bash
# 标准开发流程
@sm *draft
@dev *develop-story
@dev *run-tests
@qa *review

# 高质量开发流程
@sm *story-checklist
@dev *develop-story
@dev *run-tests
@dev *explain
@qa *review
@po *execute-checklist-po
```

#### 问题解决组合
```bash
# 需求不清晰时
@analyst *elicit
@pm *research "具体问题域"
@po *create-story

# 技术方案有问题时
@architect *research "技术难点"
@architect *execute-checklist
@dev *develop-story

# 质量问题时
@qa *review
@dev *run-tests
@sm *correct-course
```

### 时间管理和效率优化

#### 时间估算指南

**快速任务（5-15分钟）：**
- `*help` - 查看命令帮助
- `*doc-out` - 输出文档
- `*shard-prd` - 文档分片
- `*execute-checklist` - 运行检查清单

**中等任务（30分钟-2小时）：**
- `*document-project` - 项目分析
- `*create-competitor-analysis` - 竞品分析
- `*create-front-end-spec` - 前端规范
- `*review` - 质量审查

**复杂任务（2-8小时）：**
- `*create` - PRD创建
- `*create-full-stack-architecture` - 架构设计
- `*develop-story` - 功能开发
- `*perform-market-research` - 市场研究

#### 效率优化建议

**1. 批量处理**
```bash
# 一次性完成所有分析任务
@analyst *create-project-brief
@analyst *perform-market-research
@analyst *create-competitor-analysis
@analyst *elicit
```

**2. 模板复用**
```bash
# 使用已有模板快速创建
@pm *create  # 使用PRD模板
@architect *create-full-stack-architecture  # 使用架构模板
```

**3. 增量改进**
```bash
# 先快速完成，再逐步优化
@pm *create  # 快速PRD
@pm *research "深度分析"  # 深化需求
@pm *shard-prd  # 结构化文档
```

### 错误预防和处理

#### 常见错误模式

**1. 跳过需求分析**
```bash
# 错误：直接开始开发
@dev *develop-story

# 正确：先分析需求
@analyst *elicit
@pm *create
@dev *develop-story
```

**2. 忽略质量检查**
```bash
# 错误：开发完就结束
@dev *develop-story

# 正确：包含质量环节
@dev *develop-story
@dev *run-tests
@qa *review
```

**3. 文档不及时**
```bash
# 错误：最后才整理文档
# 正确：过程中持续文档化
@pm *create
@pm *shard-prd  # 及时分片
@architect *create-full-stack-architecture
@bmad-master *doc-out  # 定期输出
```

#### 错误恢复策略

**需求错误恢复：**
```bash
@po *correct-course
@analyst *elicit
@pm *research "问题域深度分析"
```

**技术错误恢复：**
```bash
@architect *research "替代方案"
@architect *execute-checklist
@dev *develop-story
```

**质量错误恢复：**
```bash
@qa *review
@dev *run-tests
@sm *story-checklist
```

### 团队协作最佳实践

#### 角色分工建议

**项目经理角色：**
- 主要使用：`@pm`, `@po`, `@sm`
- 关注点：需求管理、进度控制、质量保证

**技术负责人角色：**
- 主要使用：`@architect`, `@dev`, `@qa`
- 关注点：技术方案、代码质量、系统设计

**业务分析师角色：**
- 主要使用：`@analyst`, `@ux-expert`
- 关注点：需求分析、用户体验、市场研究

#### 协作沟通模式

**同步协作：**
```bash
# 团队会议中实时使用
@pm *create  # PM现场创建PRD
@architect *create-full-stack-architecture  # 架构师实时设计
@qa *review  # QA现场审查
```

**异步协作：**
```bash
# 分工完成，结果共享
# 成员A：@analyst *perform-market-research
# 成员B：@architect *create-backend-architecture
# 成员C：@ux-expert *create-front-end-spec
# 最后：@bmad-master *doc-out  # 整合所有结果
```

## 🔧 故障排除与问题解决

### 系统级问题诊断

#### 环境检查命令
```bash
# 1. 系统状态全面检查
python .augment/bmad_augment.py status

# 预期输出：
# ✅ BMad-Method v4.33.1 运行正常
# ✅ 代理配置文件完整
# ✅ Augment Code集成正常

# 2. 配置文件验证
python .augment/validate_config.py

# 预期输出：
# ✅ .bmad-core 目录结构正确
# ✅ 代理配置文件语法正确
# ✅ 模板和任务文件完整

# 3. 详细日志查看
tail -f .ai/augment-debug.log

# 4. 代理连接测试
python .augment/bmad_augment.py list

# 预期输出：可用代理列表
```

#### 环境修复命令
```bash
# 重新初始化BMad环境
python .augment/bmad_augment.py init

# 重新加载代理配置
python .augment/bmad_augment.py reload

# 清理缓存和临时文件
python .augment/bmad_augment.py clean
```

### 代理级问题解决

#### 代理无法激活
**问题症状：** `@代理名` 无响应或报错

**诊断步骤：**
```bash
# 1. 检查代理名称拼写
@bmad-master *help  # 确认代理是否存在

# 2. 验证代理配置
python .augment/validate_config.py

# 3. 手动激活代理
python .augment/call_agent.py bmad-master "测试连接"
```

**解决方案：**
```bash
# 方案1：重新加载代理
python .augment/bmad_augment.py reload

# 方案2：使用完整代理名
@bmad-master  # 而不是 @master

# 方案3：检查代理配置文件
# 查看 .augment/agents/ 目录下的配置文件
```

#### 命令执行失败
**问题症状：** 命令有响应但执行失败

**诊断步骤：**
```bash
# 1. 确认命令语法
@pm *help  # 查看正确的命令格式

# 2. 检查命令参数
@pm *research "正确的参数格式"  # 而不是 @pm research

# 3. 验证文件权限
ls -la docs/  # 检查文件读写权限
```

**解决方案：**
```bash
# 方案1：使用正确的命令前缀
@pm *create  # 命令必须以 * 开头

# 方案2：检查参数格式
@architect *research "微服务架构"  # 参数用引号包围

# 方案3：修复文件权限
chmod 755 docs/
chmod 644 docs/*.md
```

### 功能级问题解决

#### 文档生成问题

**问题1：PRD创建失败**
```bash
# 诊断
@pm *help  # 确认create命令可用

# 解决方案
@pm *create  # 重新执行
# 如果仍失败，尝试：
@bmad-master *create-doc prd-tmpl  # 使用通用文档创建
```

**问题2：架构文档不完整**
```bash
# 诊断
@architect *execute-checklist  # 检查架构质量

# 解决方案
@architect *research "架构设计最佳实践"  # 补充研究
@architect *create-full-stack-architecture  # 重新创建
```

**问题3：文档分片失败**
```bash
# 诊断
ls -la docs/prd.md  # 确认源文件存在

# 解决方案
@pm *create  # 先确保PRD存在
@pm *shard-prd  # 再执行分片
# 或手动指定路径：
@bmad-master *shard-doc "docs/prd.md" "docs/prd/"
```

#### 开发流程问题

**问题1：故事开发失败**
```bash
# 诊断
ls -la docs/stories/  # 检查故事文件是否存在

# 解决方案
@sm *draft  # 先创建故事
@sm *story-checklist  # 验证故事质量
@dev *develop-story  # 再开始开发
```

**问题2：测试执行失败**
```bash
# 诊断
@dev *explain  # 了解当前开发状态

# 解决方案
# 确保开发环境正确配置
npm install  # 或相应的依赖安装命令
@dev *run-tests  # 重新执行测试
```

**问题3：质量审查无结果**
```bash
# 诊断
ls -la docs/stories/story-*.md  # 确认有故事文件

# 解决方案
@qa *review story-001  # 指定具体故事
# 或
@qa *review  # 审查最新故事
```

### 性能问题优化

#### 命令执行缓慢
**问题症状：** 命令响应时间超过30秒

**诊断和解决：**
```bash
# 1. 检查系统资源
top  # 查看CPU和内存使用

# 2. 清理缓存
python .augment/bmad_augment.py clean

# 3. 重启BMad服务
python .augment/bmad_augment.py restart

# 4. 使用轻量级命令
@bmad-master *help  # 而不是复杂的创建命令
```

#### 大文件处理问题
**问题症状：** 处理大型PRD或架构文档时失败

**解决方案：**
```bash
# 1. 分步处理
@pm *create  # 先创建基础PRD
@pm *shard-prd  # 立即分片
# 然后分别完善各个部分

# 2. 使用增量方式
@architect *create-backend-architecture  # 先后端
@architect *create-front-end-architecture  # 再前端
# 最后整合

# 3. 清理临时文件
rm -rf .ai/temp/*  # 清理临时文件
```

### 协作问题解决

#### 多人同时使用冲突
**问题症状：** 文件被其他用户锁定或修改冲突

**解决方案：**
```bash
# 1. 检查文件状态
git status  # 查看文件变更状态

# 2. 协调工作流
# 团队成员A：负责需求分析
@analyst *perform-market-research
@pm *create

# 团队成员B：负责架构设计
@architect *create-full-stack-architecture

# 团队成员C：负责用户体验
@ux-expert *create-front-end-spec

# 3. 最后统一整合
@bmad-master *doc-out
```

#### 代理状态不一致
**问题症状：** 不同代理看到的项目状态不同

**解决方案：**
```bash
# 1. 同步项目状态
@bmad-master *document-project  # 重新分析项目

# 2. 清理并重新开始
python .augment/bmad_augment.py clean
@bmad-master *document-project

# 3. 确保文件一致性
git add .
git commit -m "同步项目状态"
```

### 错误代码参考

#### 常见错误码及解决方案

**ERROR_001: 代理未找到**
```bash
# 错误信息：Agent 'xxx' not found
# 解决方案：
python .augment/bmad_augment.py list  # 查看可用代理
@bmad-master *help  # 使用正确的代理名
```

**ERROR_002: 命令无效**
```bash
# 错误信息：Invalid command 'xxx'
# 解决方案：
@代理名 *help  # 查看可用命令
# 确保命令以 * 开头
```

**ERROR_003: 文件权限错误**
```bash
# 错误信息：Permission denied
# 解决方案：
chmod 755 docs/
sudo chown -R $USER:$USER .bmad-core/
```

**ERROR_004: 资源不足**
```bash
# 错误信息：Out of memory or disk space
# 解决方案：
df -h  # 检查磁盘空间
free -m  # 检查内存
python .augment/bmad_augment.py clean  # 清理缓存
```

### 紧急恢复程序

#### 完全重置BMad环境
```bash
# 1. 备份重要文件
cp -r docs/ backup/
cp -r .bmad-core/ backup/

# 2. 重置环境
python .augment/bmad_augment.py reset

# 3. 重新初始化
python .augment/bmad_augment.py init

# 4. 验证恢复
@bmad-master *help
```

#### 项目状态恢复
```bash
# 1. 重新分析项目
@bmad-master *document-project

# 2. 恢复关键文档
@pm *create  # 重建PRD
@architect *create-full-stack-architecture  # 重建架构

# 3. 验证完整性
@bmad-master *doc-out
```

### 预防性维护

#### 定期检查清单
```bash
# 每周执行
python .augment/validate_config.py
python .augment/bmad_augment.py status

# 每月执行
python .augment/bmad_augment.py clean
@bmad-master *document-project

# 每季度执行
# 备份整个BMad配置
tar -czf bmad-backup-$(date +%Y%m%d).tar.gz .bmad-core/
```

#### 性能监控
```bash
# 监控命令执行时间
time @pm *create

# 监控文件大小
du -sh docs/

# 监控系统资源
@bmad-master *task performance-monitor
```

## 📚 高级使用样例

### 复杂项目管理样例

#### 样例：电商平台开发完整流程
```bash
# 阶段1：项目启动和需求分析
@bmad-master
*document-project

@analyst
*create-project-brief
*perform-market-research
*create-competitor-analysis

@pm
*create
*research "电商用户行为分析"

# 阶段2：架构设计和技术选型
@architect
*create-full-stack-architecture
*research "高并发电商架构"
*execute-checklist

# 阶段3：用户体验设计
@ux-expert
*create-front-end-spec
*generate-ui-prompt

# 阶段4：敏捷开发管理
@sm
*draft
*story-checklist

# 阶段5：开发实施
@dev
*develop-story
*run-tests
*explain

# 阶段6：质量保证
@qa
*review

# 阶段7：文档整理和发布
@bmad-master
*doc-out
*shard-doc
```

### 团队协作样例

#### 样例：多角色协作开发用户认证模块
```bash
# PM定义需求
@pm
*create-story

# 架构师设计方案
@architect
*create-backend-architecture

# UX设计界面
@ux-expert
*create-front-end-spec

# 开发者实现功能
@dev
*develop-story

# QA审查质量
@qa
*review

# SM跟踪进度
@sm
*story-checklist
```

## 🎨 自定义命令样例

### 创建自定义任务
```yaml
# 在 .bmad-core/tasks/ 目录下创建 custom-task.md
name: "自定义数据分析任务"
description: "执行特定的数据分析流程"
steps:
  - "收集数据源"
  - "清洗和预处理"
  - "统计分析"
  - "生成报告"
```

### 创建自定义模板
```yaml
# 在 .bmad-core/templates/ 目录下创建 custom-template.yaml
name: "API设计文档模板"
sections:
  - title: "API概述"
    content: "描述API的主要功能和用途"
  - title: "接口列表"
    content: "详细的接口定义和参数说明"
  - title: "错误码"
    content: "错误处理和状态码定义"
```

## 🔍 调试和监控

### 启用详细日志
```bash
# 设置环境变量启用调试模式
export BMAD_DEBUG=true

# 查看详细执行日志
tail -f .ai/augment-debug.log

# 监控代理执行状态
python .augment/bmad_augment.py monitor
```

### 性能监控
```bash
# 查看代理执行时间
@bmad-master
*task performance-monitor

# 分析任务执行效率
@analyst
*brainstorm "工作流优化"
```

## 📊 统计信息与快速参考

### 命令使用统计
- **总代理数量：** 9个核心代理
- **总命令数量：** 57个专用命令
- **工作流样例：** 5个完整场景流程
- **最常用命令：** `*help`, `*create`, `*develop-story`, `*document-project`
- **最复杂工作流：** 新项目开发完整流程（23个步骤，6周）

### 代理功能覆盖
- **需求管理：** PM, PO, Analyst
- **技术设计：** Architect, UX Expert
- **开发实施：** Dev, QA
- **项目管理：** SM, BMad Master

## 🔍 快速命令索引

### 按功能分类的命令速查

#### 项目启动类
| 命令 | 代理 | 用途 | 时间估算 |
|------|------|------|----------|
| `*document-project` | @bmad-master | 项目全面分析 | 30分钟 |
| `*create-project-brief` | @analyst | 项目简介创建 | 1小时 |
| `*create` | @pm | PRD文档创建 | 3小时 |
| `*create-full-stack-architecture` | @architect | 全栈架构设计 | 4小时 |

#### 需求分析类
| 命令 | 代理 | 用途 | 时间估算 |
|------|------|------|----------|
| `*perform-market-research` | @analyst | 市场研究 | 2小时 |
| `*create-competitor-analysis` | @analyst | 竞品分析 | 2小时 |
| `*elicit` | @analyst | 需求深度挖掘 | 1小时 |
| `*research` | @pm/@architect | 专项研究 | 1-2小时 |

#### 设计开发类
| 命令 | 代理 | 用途 | 时间估算 |
|------|------|------|----------|
| `*create-front-end-spec` | @ux-expert | 前端规范 | 3小时 |
| `*draft` | @sm | 用户故事创建 | 2小时 |
| `*develop-story` | @dev | 功能开发 | 1-5天 |
| `*run-tests` | @dev | 测试执行 | 30分钟 |

#### 质量保证类
| 命令 | 代理 | 用途 | 时间估算 |
|------|------|------|----------|
| `*review` | @qa | 质量审查 | 1-2小时 |
| `*execute-checklist` | @architect/@po | 检查清单 | 30分钟 |
| `*story-checklist` | @sm | 故事质量检查 | 30分钟 |
| `*correct-course` | @po/@sm | 项目纠偏 | 1小时 |

#### 文档管理类
| 命令 | 代理 | 用途 | 时间估算 |
|------|------|------|----------|
| `*doc-out` | @bmad-master | 文档输出 | 15分钟 |
| `*shard-prd` | @pm | PRD分片 | 15分钟 |
| `*shard-doc` | @bmad-master | 通用文档分片 | 15分钟 |
| `*explain` | @dev | 过程解释 | 30分钟 |

### 按场景分类的工作流速查

#### 新项目开发
```bash
# 快速启动（2小时）
@bmad-master *document-project
@pm *create
@architect *create-full-stack-architecture

# 完整流程（6周）
# 参见：工作流样例1
```

#### 现有项目优化
```bash
# 快速分析（1小时）
@bmad-master *document-project
@architect *create-brownfield-architecture

# 完整改进（4周）
# 参见：工作流样例2
```

#### 技术架构设计
```bash
# 快速设计（4小时）
@architect *create-full-stack-architecture
@architect *execute-checklist

# 完整设计（3周）
# 参见：工作流样例3
```

#### 产品需求分析
```bash
# 快速需求（2小时）
@pm *create
@pm *shard-prd

# 完整分析（2周）
# 参见：工作流样例4
```

#### 质量保证流程
```bash
# 快速检查（30分钟）
@qa *review
@dev *run-tests

# 完整质量体系（2周）
# 参见：工作流样例5
```

### 常见问题快速解决

#### 代理问题
```bash
# 代理无响应
python .augment/bmad_augment.py status
@bmad-master *help

# 命令无效
@代理名 *help  # 查看正确命令
```

#### 文档问题
```bash
# PRD创建失败
@pm *help
@pm *create

# 架构文档不完整
@architect *execute-checklist
@architect *research "架构设计"
```

#### 开发问题
```bash
# 故事开发失败
@sm *draft
@sm *story-checklist
@dev *develop-story

# 测试失败
@dev *explain
@dev *run-tests
```

### 团队角色命令映射

#### 项目经理常用命令
```bash
@pm *create          # 创建PRD
@pm *research        # 深度研究
@pm *shard-prd       # 文档管理
@po *create-epic     # Epic规划
@po *create-story    # 故事创建
```

#### 技术负责人常用命令
```bash
@architect *create-full-stack-architecture
@architect *create-backend-architecture
@architect *research
@architect *execute-checklist
@dev *develop-story
@dev *run-tests
```

#### 业务分析师常用命令
```bash
@analyst *create-project-brief
@analyst *perform-market-research
@analyst *create-competitor-analysis
@analyst *elicit
@analyst *brainstorm
```

#### QA工程师常用命令
```bash
@qa *review
@dev *run-tests
@sm *story-checklist
@architect *execute-checklist
```

## 📚 学习路径建议

### 初学者路径（第1周）
1. 熟悉基础命令：`*help`, `*document-project`, `*create`
2. 练习简单工作流：项目分析 → PRD创建 → 文档输出
3. 掌握故障排除：系统检查、配置验证

### 进阶用户路径（第2-3周）
1. 掌握完整工作流：新项目开发流程
2. 学习代理协作：多代理配合使用
3. 优化工作效率：命令组合、批量处理

### 高级用户路径（第4周+）
1. 自定义工作流：根据团队需求调整流程
2. 性能优化：监控和调优BMad使用
3. 团队培训：指导其他成员使用BMad

---

**文档维护者：** BMad Master
**最后更新：** 2025年8月1日
**版本：** v4.33.1 Enhanced
**适用环境：** Augment Code集成环境
**文档状态：** 完整增强版本，包含5个详细工作流样例和完整故障排除指南
**更新内容：**
- 新增5个完整工作流样例（新项目开发、现有项目优化、技术架构设计、产品需求分析、质量保证流程）
- 大幅扩展使用技巧与最佳实践
- 完善故障排除和问题解决方案
- 增加快速命令索引和学习路径建议
