import { ipcMain } from 'electron'
import { setupBookIPC } from './book-ipc'
import { setupFileIPC } from './file-ipc'
import { setupSettingsIPC } from './settings-ipc'
import { setupLearningIPC } from './learning-ipc'

/**
 * IPC通信设置模块
 * 负责设置主进程和渲染进程之间的通信
 */

/**
 * 设置所有IPC处理器
 */
export function setupIPC(): void {
  console.log('设置IPC通信处理器...')

  // 设置各个模块的IPC处理器
  setupBookIPC()
  setupFileIPC()
  setupSettingsIPC()
  setupLearningIPC()

  // 设置通用IPC处理器
  setupCommonIPC()

  console.log('IPC通信处理器设置完成')
}

/**
 * 设置通用IPC处理器
 */
function setupCommonIPC(): void {
  // 应用信息
  ipcMain.handle('app:getVersion', () => {
    const { app } = require('electron')
    return app.getVersion()
  })

  ipcMain.handle('app:getName', () => {
    const { app } = require('electron')
    return app.getName()
  })

  ipcMain.handle('app:getPath', (_, name: string) => {
    const { app } = require('electron')
    return app.getPath(name as any)
  })

  // 系统信息
  ipcMain.handle('system:getPlatform', () => {
    return process.platform
  })

  ipcMain.handle('system:getArch', () => {
    return process.arch
  })

  ipcMain.handle('system:getNodeVersion', () => {
    return process.version
  })

  // 窗口控制
  ipcMain.handle('window:minimize', () => {
    const { BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    if (window) {
      window.minimize()
    }
  })

  ipcMain.handle('window:maximize', () => {
    const { BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    if (window) {
      if (window.isMaximized()) {
        window.unmaximize()
      } else {
        window.maximize()
      }
    }
  })

  ipcMain.handle('window:close', () => {
    const { BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    if (window) {
      window.close()
    }
  })

  ipcMain.handle('window:isMaximized', () => {
    const { BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    return window ? window.isMaximized() : false
  })

  ipcMain.handle('window:toggleFullscreen', () => {
    const { BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    if (window) {
      window.setFullScreen(!window.isFullScreen())
    }
  })

  // 对话框
  ipcMain.handle('dialog:showMessageBox', async (_, options) => {
    const { dialog, BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    return await dialog.showMessageBox(window, options)
  })

  ipcMain.handle('dialog:showErrorBox', (_, title: string, content: string) => {
    const { dialog } = require('electron')
    dialog.showErrorBox(title, content)
  })

  ipcMain.handle('dialog:showOpenDialog', async (_, options) => {
    const { dialog, BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    return await dialog.showOpenDialog(window, options)
  })

  ipcMain.handle('dialog:showSaveDialog', async (_, options) => {
    const { dialog, BrowserWindow } = require('electron')
    const window = BrowserWindow.getFocusedWindow()
    return await dialog.showSaveDialog(window, options)
  })

  // 剪贴板
  ipcMain.handle('clipboard:writeText', (_, text: string) => {
    const { clipboard } = require('electron')
    clipboard.writeText(text)
  })

  ipcMain.handle('clipboard:readText', () => {
    const { clipboard } = require('electron')
    return clipboard.readText()
  })

  // Shell操作
  ipcMain.handle('shell:openExternal', async (_, url: string) => {
    const { shell } = require('electron')
    return await shell.openExternal(url)
  })

  ipcMain.handle('shell:showItemInFolder', (_, fullPath: string) => {
    const { shell } = require('electron')
    shell.showItemInFolder(fullPath)
  })

  ipcMain.handle('shell:openPath', async (_, path: string) => {
    const { shell } = require('electron')
    return await shell.openPath(path)
  })

  // 通知
  ipcMain.handle('notification:show', (_, options) => {
    const { Notification } = require('electron')
    if (Notification.isSupported()) {
      const notification = new Notification(options)
      notification.show()
      return true
    }
    return false
  })

  // 数据库健康检查
  ipcMain.handle('database:healthCheck', () => {
    const { checkDatabaseHealth } = require('../database')
    return checkDatabaseHealth()
  })

  // 错误报告
  ipcMain.handle('error:report', (_, error: any) => {
    console.error('渲染进程错误报告:', error)
    // 这里可以添加错误报告逻辑，如发送到错误监控服务
  })

  // 日志记录
  ipcMain.handle('log:info', (_, message: string, ...args: any[]) => {
    console.log('[Renderer]', message, ...args)
  })

  ipcMain.handle('log:warn', (_, message: string, ...args: any[]) => {
    console.warn('[Renderer]', message, ...args)
  })

  ipcMain.handle('log:error', (_, message: string, ...args: any[]) => {
    console.error('[Renderer]', message, ...args)
  })
}

/**
 * IPC错误处理包装器
 */
export function wrapIPCHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R> | R
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args)
    } catch (error) {
      console.error('IPC处理器错误:', error)
      throw error
    }
  }
}

/**
 * 移除所有IPC监听器
 */
export function removeAllIPCListeners(): void {
  ipcMain.removeAllListeners()
  console.log('已移除所有IPC监听器')
}
