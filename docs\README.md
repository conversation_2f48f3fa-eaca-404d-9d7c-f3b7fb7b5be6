# 📚 BMad-Method 项目文档目录

## 目录结构说明

本目录包含BMad-Method框架的所有项目文档，按照功能和用途进行分类组织。

### 📋 产品需求文档 (prd/)
存储产品需求相关的所有文档，支持分片管理和版本控制。

**子目录：**
- `epics/` - Epic级别的需求文档
- `stories/` - 用户故事详细文档  
- `requirements/` - 功能和非功能需求文档

### 📖 用户故事管理 (stories/)
按照开发状态组织用户故事，支持敏捷开发流程。

**子目录：**
- `backlog/` - 待开发的用户故事
- `in-progress/` - 正在开发中的故事
- `completed/` - 已完成的故事

### 🏗️ 架构决策记录 (adr/)
记录重要的架构决策和技术选型，保持决策的可追溯性。

**子目录：**
- `templates/` - ADR文档模板
- `decisions/` - 具体的架构决策记录

### 🔧 系统架构文档 (architecture/)
存储系统架构设计和技术规范文档。

**子目录：**
- `diagrams/` - 架构图和设计图
- `specifications/` - 技术规范文档
- `reviews/` - 架构审查记录

## 使用指南

### 文档创建流程
1. 使用BMad代理创建文档：`@pm *create`
2. 文档分片管理：`@pm *shard-prd`
3. 版本控制：使用Git管理文档变更

### 文档命名规范
- 使用小写字母和连字符
- 包含日期和版本号
- 示例：`user-authentication-epic-v1.0-20250801.md`

### 质量标准
- 所有文档必须包含创建日期和负责人
- 重要决策需要经过团队审查
- 定期更新和维护文档内容

## 相关工具

- **BMad-Method框架**：自动化文档生成和管理
- **Git版本控制**：文档版本管理和协作
- **Markdown格式**：统一的文档格式标准

---

**维护者：** BMad Master  
**创建时间：** 2025年8月1日  
**更新频率：** 根据项目需要实时更新
