import { BrowserWindow, screen } from 'electron'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'

/**
 * 窗口管理模块
 * 负责创建和管理应用窗口
 */

/**
 * 窗口配置接口
 */
interface WindowConfig {
  width: number
  height: number
  minWidth: number
  minHeight: number
  x?: number
  y?: number
}

/**
 * 获取默认窗口配置
 */
function getDefaultWindowConfig(): WindowConfig {
  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize
  
  // 计算窗口大小（屏幕的80%，但不超过最大值）
  const maxWidth = 1400
  const maxHeight = 900
  const width = Math.min(Math.floor(screenWidth * 0.8), maxWidth)
  const height = Math.min(Math.floor(screenHeight * 0.8), maxHeight)
  
  // 计算窗口位置（居中）
  const x = Math.floor((screenWidth - width) / 2)
  const y = Math.floor((screenHeight - height) / 2)
  
  return {
    width,
    height,
    minWidth: 1000,
    minHeight: 700,
    x,
    y
  }
}

/**
 * 创建主窗口
 */
export function createMainWindow(icon?: string): BrowserWindow {
  const config = getDefaultWindowConfig()
  
  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: config.width,
    height: config.height,
    minWidth: config.minWidth,
    minHeight: config.minHeight,
    x: config.x,
    y: config.y,
    show: false, // 初始不显示，等待ready-to-show事件
    autoHideMenuBar: true, // 自动隐藏菜单栏
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    icon: icon,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false, // 暂时禁用沙箱模式以支持Node.js API
      contextIsolation: true, // 启用上下文隔离
      enableRemoteModule: false, // 禁用remote模块
      nodeIntegration: false, // 禁用Node.js集成
      webSecurity: true, // 启用web安全
      allowRunningInsecureContent: false, // 不允许运行不安全内容
      experimentalFeatures: false // 禁用实验性功能
    }
  })

  // 窗口准备显示时显示窗口
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    
    // 开发环境下聚焦窗口
    if (is.dev) {
      mainWindow.focus()
    }
  })

  // 加载应用内容
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    // 开发环境：加载开发服务器
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    // 生产环境：加载构建后的文件
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // 窗口事件处理
  setupWindowEvents(mainWindow)

  return mainWindow
}

/**
 * 设置窗口事件处理
 */
function setupWindowEvents(window: BrowserWindow): void {
  // 窗口关闭前的处理
  window.on('close', (event) => {
    // 可以在这里添加保存数据的逻辑
    console.log('窗口即将关闭')
  })

  // 窗口关闭后的处理
  window.on('closed', () => {
    console.log('窗口已关闭')
  })

  // 窗口最小化处理
  window.on('minimize', () => {
    console.log('窗口已最小化')
  })

  // 窗口恢复处理
  window.on('restore', () => {
    console.log('窗口已恢复')
  })

  // 窗口大小改变处理
  window.on('resize', () => {
    const [width, height] = window.getSize()
    console.log(`窗口大小已改变: ${width}x${height}`)
  })

  // 窗口移动处理
  window.on('move', () => {
    const [x, y] = window.getPosition()
    console.log(`窗口位置已改变: (${x}, ${y})`)
  })

  // 窗口聚焦处理
  window.on('focus', () => {
    console.log('窗口已聚焦')
  })

  // 窗口失焦处理
  window.on('blur', () => {
    console.log('窗口已失焦')
  })

  // 进入全屏处理
  window.on('enter-full-screen', () => {
    console.log('进入全屏模式')
  })

  // 退出全屏处理
  window.on('leave-full-screen', () => {
    console.log('退出全屏模式')
  })
}

/**
 * 创建关于窗口
 */
export function createAboutWindow(): BrowserWindow {
  const aboutWindow = new BrowserWindow({
    width: 400,
    height: 300,
    resizable: false,
    minimizable: false,
    maximizable: false,
    modal: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 加载关于页面
  if (is.dev) {
    aboutWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/about`)
  } else {
    aboutWindow.loadFile(join(__dirname, '../renderer/about.html'))
  }

  aboutWindow.on('ready-to-show', () => {
    aboutWindow.show()
  })

  return aboutWindow
}

/**
 * 创建设置窗口
 */
export function createSettingsWindow(): BrowserWindow {
  const settingsWindow = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    show: false,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 加载设置页面
  if (is.dev) {
    settingsWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/settings`)
  } else {
    settingsWindow.loadFile(join(__dirname, '../renderer/settings.html'))
  }

  settingsWindow.on('ready-to-show', () => {
    settingsWindow.show()
  })

  return settingsWindow
}
