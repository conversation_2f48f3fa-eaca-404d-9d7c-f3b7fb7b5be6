# 🏗️ 架构决策记录 (ADR) 目录

## 目录用途

本目录存储架构决策记录 (Architecture Decision Records)，记录项目中重要的架构决策、技术选型和设计决定，确保决策的可追溯性和团队知识的传承。

## 什么是ADR

架构决策记录是一种轻量级的文档格式，用于记录架构上的重要决策。每个ADR记录一个具体的决策，包括：
- 决策的背景和问题
- 考虑的选项
- 最终决策和理由
- 决策的后果和影响

## 目录结构

### 📝 templates/
存储ADR文档模板和格式规范。

**包含文件：**
- `adr-template.md` - 标准ADR模板
- `decision-matrix-template.md` - 决策矩阵模板
- `review-template.md` - 决策审查模板

### 📋 decisions/
存储具体的架构决策记录。

**文件命名规范：**
- `adr-{编号}-{决策主题}.md`
- 示例：`adr-001-choose-database-technology.md`

**编号规则：**
- 使用三位数字编号（001, 002, 003...）
- 按时间顺序递增
- 编号一旦分配不可更改

## ADR标准模板

### 基本结构
```markdown
# ADR-{编号}: {决策标题}

## 状态
{提议中 | 已接受 | 已弃用 | 已替代}

## 背景
{描述需要做出决策的背景和问题}

## 决策
{描述我们的决策内容}

## 理由
{解释为什么做出这个决策}

## 后果
{描述这个决策的积极和消极后果}

## 相关决策
{列出相关的其他ADR}

---
**决策者：** {决策者姓名}  
**决策日期：** {YYYY-MM-DD}  
**审查日期：** {YYYY-MM-DD}  
**状态更新：** {状态变更历史}
```

## 决策流程

### 1. 识别需要决策的问题
```bash
@architect *research "{技术问题或选型}"
```

### 2. 收集信息和选项
```bash
@analyst *create-competitor-analysis
@architect *execute-checklist
```

### 3. 创建ADR草案
使用标准模板创建ADR文档

### 4. 团队讨论和审查
- 技术团队评审
- 架构师审查
- 利益相关者确认

### 5. 决策确认和发布
- 更新ADR状态为"已接受"
- 通知相关团队成员
- 开始实施决策

## 决策类型

### 技术架构决策
- 技术栈选择
- 架构模式选择
- 数据库技术选型
- 部署策略选择

### 设计决策
- API设计规范
- 数据模型设计
- 安全策略选择
- 性能优化方案

### 流程决策
- 开发流程规范
- 测试策略选择
- 发布流程设计
- 监控方案选择

## 状态管理

### ADR状态定义
- **提议中 (Proposed)**：决策正在讨论中
- **已接受 (Accepted)**：决策已确认并开始实施
- **已弃用 (Deprecated)**：决策不再适用但保留记录
- **已替代 (Superseded)**：被新的ADR替代

### 状态变更流程
1. 状态变更需要团队讨论
2. 更新ADR文档的状态字段
3. 记录状态变更的原因和日期
4. 如果是替代关系，需要在相关ADR中交叉引用

## 质量标准

### ADR质量检查清单
- [ ] 标题清晰描述决策内容
- [ ] 背景部分充分说明问题
- [ ] 考虑了多个可选方案
- [ ] 决策理由充分且有说服力
- [ ] 后果分析全面（包括积极和消极影响）
- [ ] 相关决策引用完整

### 审查流程
1. **架构师审查**：技术合理性和架构一致性
2. **团队评审**：实施可行性和影响评估
3. **产品负责人确认**：业务价值和优先级
4. **最终确认**：正式接受决策

## 使用指南

### 何时创建ADR
- 做出重要的架构决策时
- 选择技术方案时
- 改变现有架构时
- 解决技术争议时

### 何时更新ADR
- 决策状态发生变化时
- 发现新的后果或影响时
- 相关技术环境发生变化时
- 定期审查时发现需要更新

### 何时弃用ADR
- 技术方案不再适用时
- 被更好的方案替代时
- 业务需求发生根本变化时

## 相关工具和命令

### BMad命令
```bash
@architect *research "{技术主题}"          # 技术研究
@architect *execute-checklist              # 架构检查
@analyst *create-competitor-analysis       # 方案对比
@bmad-master *doc-out                      # 文档输出
```

### 文档管理
- 使用Git进行版本控制
- 定期备份ADR文档
- 建立ADR索引和搜索机制

## 最佳实践

### 编写建议
1. **保持简洁**：每个ADR专注于一个决策
2. **客观描述**：避免主观判断，基于事实
3. **考虑全面**：分析所有可行选项
4. **后果明确**：清楚说明决策的影响

### 维护建议
1. **定期审查**：每季度审查ADR的有效性
2. **及时更新**：环境变化时及时更新状态
3. **交叉引用**：建立ADR之间的关联关系
4. **知识传承**：新团队成员必须了解相关ADR

---

**维护者：** 架构师 (Architect)  
**创建时间：** 2025年8月1日  
**审查频率：** 每季度审查一次
