# Yu Reader 技术架构设计文档

## 📋 文档信息

**项目名称：** Yu Reader (玉阅读器)  
**文档版本：** v1.0  
**创建时间：** 2025 年 8 月 1 日  
**文档类型：** 技术架构设计文档  
**基于文档：** Yu Reader PRD v3.0

## 🎯 架构概述

### 架构目标

- **高性能阅读体验**：支持多种电子书格式的流畅阅读
- **智能学习辅助**：集成 AI 功能提供个性化学习服务
- **跨平台兼容**：Windows、macOS、Linux 三平台统一体验
- **可扩展架构**：支持功能模块化扩展和第三方集成
- **数据安全**：本地优先的数据存储和隐私保护

### 核心架构原则

1. **分层架构**：清晰的业务逻辑分层
2. **模块化设计**：功能模块独立可替换
3. **事件驱动**：松耦合的组件通信
4. **本地优先**：核心功能离线可用
5. **渐进增强**：基础功能稳定，高级功能可选

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Yu Reader 桌面应用                        │
├─────────────────────────────────────────────────────────────┤
│  Electron 主进程 (Main Process)                              │
│  ├── 应用生命周期管理                                         │
│  ├── 窗口管理                                               │
│  ├── 文件系统访问                                           │
│  ├── 数据库管理                                             │
│  └── 系统集成 (菜单、通知、快捷键)                           │
├─────────────────────────────────────────────────────────────┤
│  渲染进程 (Renderer Process) - Vue3 应用                     │
│  ├── 用户界面层 (UI Layer)                                   │
│  │   ├── Element Plus 组件库                                │
│  │   ├── 自定义组件                                         │
│  │   └── 主题系统                                           │
│  ├── 状态管理层 (State Management)                           │
│  │   ├── Pinia Store                                        │
│  │   ├── 持久化插件                                         │
│  │   └── 状态同步                                           │
│  ├── 业务逻辑层 (Business Logic)                             │
│  │   ├── 阅读器核心                                         │
│  │   ├── 学习服务                                           │
│  │   ├── 书架管理                                           │
│  │   └── 笔记系统                                           │
│  └── 数据访问层 (Data Access)                               │
│      ├── API 客户端                                         │
│      ├── 本地存储                                           │
│      └── 缓存管理                                           │
├─────────────────────────────────────────────────────────────┤
│  Worker 进程 (Background Workers)                            │
│  ├── 文件解析 Worker                                        │
│  ├── AI 计算 Worker                                         │
│  ├── 索引构建 Worker                                        │
│  └── 数据同步 Worker                                        │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (Data Storage)                                   │
│  ├── SQLite3 主数据库                                       │
│  ├── IndexedDB 缓存                                         │
│  ├── 文件系统存储                                           │
│  └── 配置文件                                               │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

#### 核心框架

- **Electron 28+**：跨平台桌面应用框架
- **Vue 3.4+**：现代化前端框架，使用 Composition API
- **TypeScript 5.0+**：类型安全的 JavaScript 超集
- **Element Plus 2.4+**：Vue 3 UI 组件库

#### 状态管理与数据

- **Pinia 2.1+**：Vue 3 官方推荐状态管理
- **SQLite3**：轻量级本地数据库
- **IndexedDB**：浏览器端结构化数据存储
- **Electron Store**：应用配置持久化

#### 构建与开发工具

- **Vite 5.0+**：快速构建工具
- **Electron Builder**：应用打包和分发
- **ESLint + Prettier**：代码质量和格式化
- **Vitest**：单元测试框架

#### 专业库集成

- **epub.js**：EPUB 格式解析和渲染
- **PDF.js**：PDF 格式解析和渲染
- **mammoth.js**：DOCX 格式解析
- **jszip**：压缩文件处理
- **fuse.js**：模糊搜索引擎

## 📊 数据架构

### 数据库设计

#### 核心数据表

```sql
-- 图书信息表
CREATE TABLE books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    author TEXT,
    isbn TEXT,
    format TEXT NOT NULL, -- epub, pdf, txt, etc.
    file_path TEXT NOT NULL,
    file_size INTEGER,
    cover_path TEXT,
    language TEXT,
    publisher TEXT,
    publish_date DATE,
    description TEXT,
    tags TEXT, -- JSON array
    metadata TEXT, -- JSON object
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 阅读进度表
CREATE TABLE reading_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    current_position TEXT NOT NULL, -- JSON object with format-specific position
    progress_percentage REAL DEFAULT 0,
    reading_time INTEGER DEFAULT 0, -- seconds
    last_read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 书签表
CREATE TABLE bookmarks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    title TEXT,
    position TEXT NOT NULL, -- JSON object
    content_preview TEXT,
    color TEXT DEFAULT '#ffeb3b',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 笔记表
CREATE TABLE notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    position TEXT NOT NULL, -- JSON object
    highlight_text TEXT,
    note_type TEXT DEFAULT 'text', -- text, voice, image
    tags TEXT, -- JSON array
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 学习记录表
CREATE TABLE learning_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    word TEXT NOT NULL,
    translation TEXT,
    context TEXT,
    position TEXT NOT NULL, -- JSON object
    mastery_level INTEGER DEFAULT 0, -- 0-5
    review_count INTEGER DEFAULT 0,
    last_reviewed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

### 数据访问模式

#### Repository 模式

```typescript
// 数据访问抽象层
interface BookRepository {
  findAll(): Promise<Book[]>;
  findById(id: number): Promise<Book | null>;
  create(book: CreateBookDto): Promise<Book>;
  update(id: number, book: UpdateBookDto): Promise<Book>;
  delete(id: number): Promise<void>;
  search(query: string): Promise<Book[]>;
}

// SQLite 实现
class SQLiteBookRepository implements BookRepository {
  constructor(private db: Database) {}

  async findAll(): Promise<Book[]> {
    const stmt = this.db.prepare(
      'SELECT * FROM books ORDER BY updated_at DESC'
    );
    return stmt.all() as Book[];
  }

  // ... 其他方法实现
}
```

## 🔧 核心模块设计

### 1. 阅读引擎模块

#### 文件解析器

```typescript
interface FileParser {
  canParse(filePath: string): boolean;
  parse(filePath: string): Promise<ParsedBook>;
  getTableOfContents(book: ParsedBook): Promise<TOCItem[]>;
  getContent(book: ParsedBook, position: Position): Promise<string>;
}

class EPUBParser implements FileParser {
  async parse(filePath: string): Promise<ParsedBook> {
    const book = ePub(filePath);
    await book.ready;

    return {
      metadata: await this.extractMetadata(book),
      spine: await this.extractSpine(book),
      resources: await this.extractResources(book),
    };
  }
}
```

#### 渲染引擎

```typescript
class ReadingRenderer {
  private container: HTMLElement;
  private currentBook: ParsedBook;

  async renderContent(position: Position): Promise<void> {
    const content = await this.getContentAtPosition(position);
    const processedContent = await this.processContent(content);
    this.container.innerHTML = processedContent;
    this.attachEventListeners();
  }

  private async processContent(content: string): Promise<string> {
    // 处理样式、字体、主题等
    return this.applyTheme(this.sanitizeContent(content));
  }
}
```

### 2. AI 学习服务模块

#### 智能翻译服务

```typescript
interface TranslationService {
  translate(text: string, from: string, to: string): Promise<Translation>;
  detectLanguage(text: string): Promise<string>;
  getWordDefinition(word: string): Promise<Definition>;
}

class HybridTranslationService implements TranslationService {
  constructor(
    private localService: LocalTranslationService,
    private cloudService: CloudTranslationService
  ) {}

  async translate(
    text: string,
    from: string,
    to: string
  ): Promise<Translation> {
    try {
      // 优先使用本地服务
      return await this.localService.translate(text, from, to);
    } catch (error) {
      // 降级到云服务
      return await this.cloudService.translate(text, from, to);
    }
  }
}
```

#### 学习分析引擎

```typescript
class LearningAnalytics {
  async analyzeReadingPattern(userId: string): Promise<ReadingPattern> {
    const records = await this.getReadingRecords(userId);
    return {
      averageReadingSpeed: this.calculateReadingSpeed(records),
      preferredReadingTime: this.analyzeTimePatterns(records),
      difficultyPreference: this.analyzeDifficultyLevel(records),
      topicInterests: this.extractTopicInterests(records),
    };
  }

  async generateLearningPlan(pattern: ReadingPattern): Promise<LearningPlan> {
    // 基于阅读模式生成个性化学习计划
  }
}
```

## 🔌 API 设计

### IPC 通信接口

#### 主进程 API

```typescript
// 文件操作 API
ipcMain.handle('file:import', async (event, filePaths: string[]) => {
  return await fileService.importBooks(filePaths);
});

ipcMain.handle('file:open', async (event, bookId: number) => {
  return await fileService.openBook(bookId);
});

// 数据库操作 API
ipcMain.handle('db:books:findAll', async () => {
  return await bookRepository.findAll();
});

ipcMain.handle('db:notes:create', async (event, note: CreateNoteDto) => {
  return await noteRepository.create(note);
});
```

#### 渲染进程 API 客户端

```typescript
class ElectronAPIClient {
  async importBooks(filePaths: string[]): Promise<Book[]> {
    return await window.electronAPI.invoke('file:import', filePaths);
  }

  async getAllBooks(): Promise<Book[]> {
    return await window.electronAPI.invoke('db:books:findAll');
  }

  async createNote(note: CreateNoteDto): Promise<Note> {
    return await window.electronAPI.invoke('db:notes:create', note);
  }
}
```

## 🎨 前端架构

### 组件架构

```
src/renderer/
├── components/           # 通用组件
│   ├── base/            # 基础组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── views/               # 页面视图
│   ├── bookshelf/       # 书架页面
│   ├── reader/          # 阅读器页面
│   ├── learning/        # 学习页面
│   └── settings/        # 设置页面
├── stores/              # Pinia 状态管理
├── services/            # 业务服务
├── utils/               # 工具函数
└── types/               # TypeScript 类型定义
```

### 状态管理设计

```typescript
// 书架状态管理
export const useBookshelfStore = defineStore('bookshelf', () => {
  const books = ref<Book[]>([]);
  const currentBook = ref<Book | null>(null);
  const loading = ref(false);

  const loadBooks = async () => {
    loading.value = true;
    try {
      books.value = await apiClient.getAllBooks();
    } finally {
      loading.value = false;
    }
  };

  const importBooks = async (filePaths: string[]) => {
    const newBooks = await apiClient.importBooks(filePaths);
    books.value.push(...newBooks);
  };

  return {
    books: readonly(books),
    currentBook: readonly(currentBook),
    loading: readonly(loading),
    loadBooks,
    importBooks,
  };
});
```

## 🔒 安全架构

### 数据安全

- **本地加密**：敏感数据使用 AES-256 加密存储
- **文件完整性**：使用 SHA-256 校验文件完整性
- **访问控制**：基于用户权限的数据访问控制

### 应用安全

- **代码签名**：应用程序数字签名验证
- **沙箱模式**：渲染进程运行在沙箱环境
- **CSP 策略**：内容安全策略防止 XSS 攻击

## 📈 性能优化

### 启动优化

- **延迟加载**：非核心模块延迟初始化
- **预编译**：模板和组件预编译
- **缓存策略**：智能缓存常用数据

### 运行时优化

- **虚拟滚动**：大列表虚拟化渲染
- **Web Workers**：后台处理重计算任务
- **内存管理**：及时释放不用的资源

### 文件处理优化

- **分块加载**：大文件分块处理
- **增量解析**：按需解析文件内容
- **智能预加载**：预测性内容预加载

## 🧪 测试策略

### 测试金字塔

- **单元测试**：核心业务逻辑测试 (70%)
- **集成测试**：模块间交互测试 (20%)
- **端到端测试**：完整用户流程测试 (10%)

### 测试工具链

- **Vitest**：单元测试和集成测试
- **Playwright**：端到端测试
- **Testing Library**：组件测试
- **MSW**：API 模拟测试

## 🚀 部署架构

### 构建流程

```yaml
# GitHub Actions 构建流程
name: Build and Release
on:
  push:
    tags: ['v*']
jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run build
      - run: npm run package
```

### 分发策略

- **自动更新**：Electron 内置更新机制
- **多平台支持**：Windows (exe/msi)、macOS (dmg)、Linux (AppImage/deb)
- **增量更新**：仅下载变更部分

## 📋 技术风险评估

### 高风险项

1. **大文件性能**：PDF 大文件渲染性能
2. **内存占用**：Electron 应用内存使用
3. **格式兼容**：不同版本电子书格式兼容性

### 风险缓解策略

1. **性能监控**：实时性能指标监控
2. **渐进加载**：分块加载大文件
3. **兼容性测试**：广泛的格式兼容性测试

## 📚 技术债务管理

### 代码质量

- **代码审查**：强制代码审查流程
- **静态分析**：ESLint + SonarQube
- **技术债务跟踪**：定期技术债务评估

### 依赖管理

- **依赖更新**：定期依赖安全更新
- **许可证合规**：开源许可证合规检查
- **供应链安全**：依赖包安全扫描

## 🔮 创新功能技术实现

### 1. 智能阅读伴侣 (AI Reading Companion)

#### 技术架构

```typescript
class AIReadingCompanion {
  private nlpService: NLPService;
  private contextAnalyzer: ContextAnalyzer;
  private recommendationEngine: RecommendationEngine;

  async analyzeReadingContext(
    content: string,
    position: Position
  ): Promise<ReadingContext> {
    const entities = await this.nlpService.extractEntities(content);
    const sentiment = await this.nlpService.analyzeSentiment(content);
    const topics = await this.nlpService.extractTopics(content);

    return {
      entities,
      sentiment,
      topics,
      difficulty: await this.assessDifficulty(content),
      readingTime: this.estimateReadingTime(content),
    };
  }

  async generateInsights(context: ReadingContext): Promise<ReadingInsight[]> {
    return [
      await this.generateSummary(context),
      await this.generateQuestions(context),
      await this.generateConnections(context),
    ];
  }
}
```

#### 实现策略

- **本地 NLP 模型**：使用 TensorFlow.js 运行轻量级模型
- **云端增强**：复杂分析任务可选云端处理
- **渐进式功能**：从简单文本分析开始，逐步增加复杂功能

### 2. 自适应学习引擎 (Adaptive Learning Engine)

#### 学习模型设计

```typescript
interface LearningProfile {
  readingSpeed: number;
  comprehensionLevel: number;
  vocabularyLevel: number;
  preferredDifficulty: number;
  learningGoals: string[];
  weakAreas: string[];
}

class AdaptiveLearningEngine {
  async updateLearningProfile(
    userId: string,
    activity: LearningActivity
  ): Promise<LearningProfile> {
    const currentProfile = await this.getLearningProfile(userId);
    const updatedProfile = this.adjustProfile(currentProfile, activity);

    await this.saveLearningProfile(userId, updatedProfile);
    return updatedProfile;
  }

  async recommendNextContent(
    profile: LearningProfile
  ): Promise<ContentRecommendation[]> {
    return this.recommendationAlgorithm.generate(profile);
  }
}
```

### 3. 多模态内容理解 (Multimodal Content Understanding)

#### 技术实现

```typescript
class MultimodalProcessor {
  private textProcessor: TextProcessor;
  private imageProcessor: ImageProcessor;
  private audioProcessor: AudioProcessor;

  async processContent(content: MultimodalContent): Promise<ProcessedContent> {
    const results = await Promise.all([
      this.textProcessor.process(content.text),
      this.imageProcessor.process(content.images),
      this.audioProcessor.process(content.audio),
    ]);

    return this.combineResults(results);
  }

  async extractKnowledgeGraph(
    content: ProcessedContent
  ): Promise<KnowledgeGraph> {
    // 从多模态内容中提取知识图谱
    return this.knowledgeExtractor.extract(content);
  }
}
```

### 4. 沉浸式 3D 阅读空间 (Immersive 3D Reading Space)

#### 3D 渲染技术

```typescript
// 使用 Three.js 实现 3D 阅读环境
class Reading3DSpace {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;

  initializeSpace(): void {
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.renderer = new THREE.WebGLRenderer({ antialias: true });

    this.setupLighting();
    this.createReadingEnvironment();
    this.setupControls();
  }

  createReadingEnvironment(): void {
    // 创建虚拟书房环境
    const room = this.createRoom();
    const bookshelf = this.createBookshelf();
    const readingDesk = this.createReadingDesk();

    this.scene.add(room, bookshelf, readingDesk);
  }
}
```

#### 实现考虑

- **性能优化**：使用 LOD (Level of Detail) 技术
- **可选功能**：作为高级功能，不影响基础阅读体验
- **硬件要求**：检测硬件能力，动态调整渲染质量

### 5. 智能手势识别 (Intelligent Gesture Recognition)

#### 手势识别实现

```typescript
class GestureRecognizer {
  private handTracker: HandTracker;
  private gestureClassifier: GestureClassifier;

  async initializeCamera(): Promise<void> {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
    this.handTracker.initialize(stream);
  }

  async recognizeGesture(frame: ImageData): Promise<Gesture | null> {
    const handLandmarks = await this.handTracker.detectHands(frame);
    if (!handLandmarks) return null;

    return this.gestureClassifier.classify(handLandmarks);
  }

  mapGestureToAction(gesture: Gesture): ReadingAction {
    const gestureMap = {
      swipe_left: 'next_page',
      swipe_right: 'previous_page',
      pinch: 'zoom',
      point: 'select_text',
    };

    return gestureMap[gesture.type];
  }
}
```

## 🎯 实施优先级与阶段规划

### 阶段 1: 核心功能 MVP (8-10 周)

#### 必需功能

- ✅ 基础阅读器 (EPUB, PDF)
- ✅ 简单书架管理
- ✅ 基础笔记和书签
- ✅ 基本设置系统

#### 技术重点

- Electron + Vue3 基础架构
- SQLite 数据库设计
- 文件解析和渲染
- 基础 UI 组件

### 阶段 2: 学习功能增强 (8-10 周)

#### 核心学习功能

- ✅ 划词翻译
- ✅ 生词本管理
- ✅ 阅读统计
- ✅ 智能阅读伴侣 (基础版)

#### 技术重点

- AI 服务集成
- 学习数据分析
- 性能优化
- 用户体验改进

### 阶段 3: 高级智能功能 (8-10 周)

#### 智能功能

- ✅ 自适应学习引擎
- ✅ 多模态内容理解
- ✅ 知识图谱构建
- ✅ 协作式阅读

#### 技术重点

- 机器学习模型集成
- 复杂数据处理
- 实时协作技术
- 高级分析功能

### 阶段 4: 创新体验功能 (6-8 周)

#### 创新功能 (可选)

- 🔄 沉浸式 3D 阅读空间
- 🔄 智能手势识别
- ✅ 学习社区平台
- ✅ 跨平台同步

#### 技术重点

- 3D 渲染技术
- 计算机视觉
- 社交功能开发
- 云端服务集成

## 📊 技术可行性评估

### 高可行性功能 (风险低)

- 基础阅读器功能
- 书架管理系统
- 笔记和书签系统
- 划词翻译服务
- 阅读统计分析

### 中等可行性功能 (风险中)

- AI 阅读伴侣
- 自适应学习引擎
- 多模态内容理解
- 知识图谱构建
- 协作式阅读

### 低可行性功能 (风险高)

- 沉浸式 3D 阅读空间
- 智能手势识别
- 高级预测分析
- 复杂 AI 功能

### 风险缓解策略

1. **原型验证**：高风险功能先做原型验证
2. **渐进实现**：从简单版本开始，逐步增强
3. **可选功能**：高风险功能作为可选功能
4. **技术调研**：提前进行技术可行性调研

## 🔧 开发工具链

### 开发环境

```json
{
  "name": "yu-reader",
  "version": "1.0.0",
  "main": "dist/main/index.js",
  "scripts": {
    "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"",
    "dev:main": "electron-vite dev --main",
    "dev:renderer": "electron-vite dev --renderer",
    "build": "electron-vite build",
    "package": "electron-builder",
    "test": "vitest",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .ts,.vue",
    "format": "prettier --write ."
  },
  "devDependencies": {
    "electron": "^28.0.0",
    "electron-vite": "^2.0.0",
    "electron-builder": "^24.0.0",
    "vue": "^3.4.0",
    "typescript": "^5.0.0",
    "vite": "^5.0.0",
    "vitest": "^1.0.0",
    "playwright": "^1.40.0"
  }
}
```

### 代码质量工具

```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run lint
      - run: npm run test
      - run: npm run build
```

## 📈 性能基准

### 性能目标

- **启动时间**：< 3 秒 (冷启动)
- **文件打开**：< 1 秒 (中等大小文件)
- **页面翻转**：< 100ms
- **搜索响应**：< 500ms
- **内存占用**：< 200MB (空闲状态)

### 监控指标

```typescript
class PerformanceMonitor {
  private metrics: Map<string, number> = new Map();

  startTiming(operation: string): void {
    this.metrics.set(`${operation}_start`, performance.now());
  }

  endTiming(operation: string): number {
    const start = this.metrics.get(`${operation}_start`);
    if (!start) return 0;

    const duration = performance.now() - start;
    this.metrics.set(operation, duration);
    return duration;
  }

  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }
}
```

## 🔐 数据隐私与安全

### 隐私保护原则

1. **数据最小化**：只收集必要的数据
2. **本地优先**：敏感数据优先本地存储
3. **用户控制**：用户完全控制自己的数据
4. **透明度**：清晰的隐私政策和数据使用说明

### 安全实现

```typescript
class SecurityManager {
  private encryptionKey: CryptoKey;

  async encryptSensitiveData(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);

    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: crypto.getRandomValues(new Uint8Array(12)) },
      this.encryptionKey,
      dataBuffer
    );

    return this.arrayBufferToBase64(encrypted);
  }

  async validateFileIntegrity(filePath: string): Promise<boolean> {
    const fileBuffer = await fs.readFile(filePath);
    const hashBuffer = await crypto.subtle.digest('SHA-256', fileBuffer);
    const hash = this.arrayBufferToHex(hashBuffer);

    return this.verifyHash(filePath, hash);
  }
}
```

## 📋 技术决策记录 (ADR)

### ADR-001: 选择 Electron 作为跨平台框架

**状态**: 已接受
**日期**: 2025-08-01

**背景**: 需要选择跨平台桌面应用开发框架

**决策**: 选择 Electron

**理由**:

- 团队熟悉 Web 技术栈
- 丰富的生态系统和社区支持
- 快速开发和迭代能力
- 良好的跨平台兼容性

**后果**:

- 较高的内存占用
- 启动时间相对较慢
- 需要额外的性能优化工作

### ADR-002: 选择 Vue 3 作为前端框架

**状态**: 已接受
**日期**: 2025-08-01

**背景**: 需要选择现代化前端框架

**决策**: 选择 Vue 3 + Composition API

**理由**:

- 学习曲线相对平缓
- 优秀的 TypeScript 支持
- 活跃的社区和生态
- 适合中等复杂度的应用

**后果**:

- 需要学习 Composition API
- 某些高级功能可能需要额外开发

## 🚀 未来技术演进

### 短期目标 (6 个月)

- 完成核心功能开发
- 优化性能和用户体验
- 建立完整的测试体系
- 发布第一个稳定版本

### 中期目标 (1 年)

- 集成更多 AI 功能
- 支持更多文件格式
- 开发移动端应用
- 建立用户社区

### 长期愿景 (2-3 年)

- 成为领先的学习型阅读器
- 构建完整的学习生态系统
- 探索 AR/VR 阅读体验
- 开放平台和 API 服务

---

**文档维护**: 本文档将随着项目进展持续更新，确保技术架构与实际实现保持一致。
